{"ResponsibleGamingPlayerInfo": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "player code", "example": "PL0001"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}, "jurisdiction": {"type": "string", "description": "jurisdiction this settings are applicable to", "example": "UK"}, "settings": {"type": "object", "properties": {"casino": {"$ref": "#/definitions/ResponsibleGamingPlayerSettings"}, "sport_bet": {"$ref": "#/definitions/ResponsibleGamingPlayerSettings"}}}}}, "ResponsibleGamingPlayerSettings": {"allOf": [{"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}, {"type": "object", "properties": {"lossLimitPending": {"type": "number", "description": "Loss limit pending value", "example": 900}, "lossLimitPendingTimeframe": {"type": "string", "description": "Loss limit pending value timeframe", "enum": ["daily", "weekly", "monthly"], "example": "weekly"}, "lossLimitPendingDate": {"type": "string", "description": "Date in ISO8601 when loss limit pending value will become active", "example": "2018-06-27T12:59:46.694Z"}, "depositLimitPending": {"type": "number", "description": "Deposit limit pending value", "example": 900}, "depositLimitPendingTimeframe": {"type": "string", "description": "Deposit limit pending value timeframe", "enum": ["daily", "weekly", "monthly"], "example": "weekly"}, "depositLimitPendingDate": {"type": "string", "description": "Date in ISO8601 when loss deposit pending value will become active", "example": "2018-06-27T12:59:46.694Z"}, "createdAt": {"type": "string", "description": "time when a record was created (ISO 8601 timestamp)", "example": "2016-02-23T12:45:42.324Z"}, "updatedAt": {"type": "string", "description": "last time a record was updated (ISO 8601 timestamp)", "example": "2017-03-08T05:15:54.213Z"}}}]}, "UpdateResponsibleGamingPlayerSettings": {"type": "object", "properties": {"casino": {"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}, "sport_bet": {"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}}}, "ResponsibleGamingPlayerSettingsUpdateData": {"type": "object", "properties": {"realityCheck": {"type": "number", "description": "Reality check value. 20/40/60/120/180 mins", "example": 40}, "lossLimit": {"type": "number", "description": "Loss limit value", "example": 100}, "lossLimitTimeframe": {"type": "string", "description": "Loss limit timeframe. daily, weekly, or monthly", "enum": ["daily", "weekly", "monthly"], "example": "daily"}, "depositLimit": {"type": "number", "description": "Deposit limit value", "example": 100}, "depositLimitTimeframe": {"type": "string", "description": "Deposit limit timeframe. daily, weekly, or monthly", "enum": ["daily", "weekly", "monthly"], "example": "daily"}, "casinoTimeoutTillDate": {"type": "string", "description": "Date in ISO8601 till which player has forbidden himself to play", "example": "2018-06-27T12:59:46.694Z"}, "selfExclusionTillDate": {"type": "string", "description": "Date in ISO8601 till which player has forbidden himself to get into system", "example": "2018-06-27T12:59:46.694Z"}}}, "DeletePendingResponsibleGamingPlayerSetting": {"type": "object", "properties": {"casino": {"type": "object", "description": "Pending entry to delete", "example": {"depositLimit": "anyValue"}}}}, "ResponsibleGamingPlayerSuspensionInfo": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "player code", "example": "PL0001"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}, "productType": {"type": "string", "description": "casino or sport_bet", "example": "casino"}, "suspensionTypes": {"type": "array", "description": "list of suspension types", "items": {"type": "string"}, "example": ["time-out", "self-exclusion"]}, "createdAt": {"type": "string", "description": "Date in ISO8601 when suspension was created", "example": "2018-10-12T11:29:56.644Z"}, "endTime": {"type": "string", "description": "Date in ISO8601 when suspension will be ended", "example": "2019-02-12T11:06:00.000Z"}}}, "MoveEntity": {"type": "object", "properties": {"entityKey": {"type": "string", "example": "71E01000-0000-49e7-87fb-cd9d97f9ad12", "description": "<PERSON><PERSON><PERSON>'s key which should be moved"}, "newParentKey": {"type": "string", "example": "71E01000-0000-49e7-87fb-cd9d97f9ad12", "description": "<PERSON><PERSON>'s key"}}}, "Currency": {"type": "object", "properties": {"displayName": {"type": "string", "example": "US dollar"}, "code": {"type": "string", "description": "[ISO 4217](http://en.wikipedia.org/wiki/ISO_4217) currency code.", "example": "USD"}}}, "Countries": {"type": "array", "items": {"type": "object", "properties": {"displayName": {"type": "string", "example": "United States"}, "code": {"type": "string", "description": "[ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) country code.", "example": "US"}}}}, "LanguagesToModify": {"description": "", "type": "array", "items": {"type": "string", "pattern": "^[a-z]{2}$"}}, "Languages": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Language name", "example": "English"}, "nativeName": {"type": "string", "description": "Native name.", "example": "English"}, "direction": {"type": "string", "description": "Language direction. Possible values: ltr (Left-to-right text), rtl (Right-to-left text).", "example": "ltr", "enum": ["ltr", "rtl"]}, "code": {"type": "string", "description": "[ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1) language code.", "example": "en"}}}}, "MerchantTypes": {"type": "array", "items": {"type": "object", "required": ["type", "schema"], "properties": {"type": {"type": "string", "description": "Merchant type", "example": "ipm"}, "url": {"type": "string", "description": "Link to the adapter url for this type of integration", "example": "http://localhost:1234/"}, "mainDomainUrl": {"type": "string", "description": "Link to the main domain adapter url for this type of integration", "example": "http://localhost:1235/"}, "schema": {"type": "object", "description": "Description of merchant params, is used to create merchant by client. Structure can ", "properties": {"serverUrl": {"type": "object", "properties": {"type": {"type": "string", "description": "type of field of form", "example": "text"}, "title": {"type": "string", "description": "Label for for form input", "example": "Server url"}, "defaultValue": {"type": "string", "description": "Default value of form", "example": "http://localhost:4321/"}}}}}}}}, "MerchantType": {"type": "object", "required": ["type", "schema"], "properties": {"type": {"type": "string", "description": "Merchant type", "example": "ipm"}, "url": {"type": "string", "description": "Link to the adapter url for this type of integration", "example": "http://localhost:1234/"}, "schema": {"type": "object", "description": "Description of merchant params, is used to create merchant by client. Structure can ", "properties": {"serverUrl": {"type": "object", "description": "Need to specify if adapter is external", "properties": {"type": {"type": "string", "description": "type of field of form", "example": "text"}, "title": {"type": "string", "description": "Label for for form input", "example": "Server url"}, "defaultValue": {"type": "string", "description": "Default value of form", "example": "http://localhost:4321/"}}}}}}}, "UpdateMerchantType": {"type": "object", "properties": {"url": {"type": "string", "description": "Link to the adapter url for this type of integration", "example": "http://localhost:1234/"}, "schema": {"type": "object", "description": "Description of merchant params, is used to create merchant by client. Structure can ", "properties": {"serverUrl": {"type": "object", "description": "Need to specify if adapter is external", "properties": {"type": {"type": "string", "description": "type of field of form", "example": "text"}, "title": {"type": "string", "description": "Label for for form input", "example": "Server url"}, "defaultValue": {"type": "string", "description": "Default value of form", "example": "http://localhost:4321/"}}}}}}}, "SchemaDefinitions": {"type": "array", "items": {"$ref": "#/definitions/SchemaDefinition"}}, "SchemaDefinition": {"allOf": [{"type": "object", "properties": {"id": {"type": "string", "example": "hu2s38S", "description": "Id of schema definition"}}}, {"$ref": "#/definitions/CreateSchemaDefinition"}]}, "CreateSchemaDefinition": {"allOf": [{"type": "object", "properties": {"name": {"type": "string", "example": "Slot Games", "description": "Unique name of schema definition"}}}, {"$ref": "#/definitions/UpdateSchemaDefinition"}]}, "UpdateSchemaDefinition": {"type": "object", "properties": {"schema": {"type": "object", "description": "Json schema definition for game limits, each property required field \"limitConfigurationType\". Possible values: \"configurable\", \"calculated\", \"fixed\", \"constForAllCurrencies\"", "example": {"type": "object", "properties": {"stakeAll": {"type": "array", "title": "Stakes", "limitConfigurationType": "configurable", "items": {"type": "number"}}, "defaultTotalStake": {"type": "number", "title": "Default Total Bet", "limitConfigurationType": "configurable", "applySmartRounding": "true"}, "winMax": {"type": "number", "title": "Win Capping", "limitConfigurationType": "configurable"}, "stakeDef": {"type": "number", "limitConfigurationType": "calculated"}, "stakeMax": {"type": "number", "limitConfigurationType": "calculated"}, "maxTotalStake": {"type": "number", "limitConfigurationType": "calculated"}, "stakeMin": {"type": "number", "limitConfigurationType": "calculated"}, "coins": {"type": "array", "title": "Available coins", "limitConfigurationType": "constForAllCurrencies", "items": {"type": "number"}}}}}, "required": {"type": "array", "items": {"type": "string"}, "example": ["stakeAll", "defaultTotalStake", "winMax", "coins"], "description": "Array should not contain calculated properties, if property not require for game launch or calculation of other properties in should be exclude from array, otherwise on schema limits creation this value will be required"}, "permissions": {"type": "object", "description": "Permission allows to specify who can edit property of game limits, also it allows to specify separate permissions by each currency. Default permissions is for all currencies if it's not specified.", "properties": {"default": {"type": "object", "properties": {"stakeAll": {"type": "string", "description": "Property can be edited by entity in game limits configuration, possible values: entity, admin", "example": "entity"}, "defaultTotalStake": {"type": "string", "description": "Property can be edited by entity in game limits configuration", "example": "entity"}, "winMax": {"type": "string", "description": "Property can be edited only by admin", "example": "admin"}}}, "BNS": {"type": "object", "properties": {"stakeAll": {"type": "string", "description": "Property can be edited by entity in game limits configuration, possible values: entity, admin", "example": "admin"}, "defaultTotalStake": {"type": "string", "description": "Property can be edited by entity in game limits configuration", "example": "admin"}, "winMax": {"type": "string", "description": "Property can be edited only by admin", "example": "admin"}}}}}}}, "ExtendedGameLimitsInfo": {"type": "object", "properties": {"stakeAll": {"type": "object", "example": {"calculated": false, "calculatedFromBase": true, "custom": false, "inherited": false, "value": [0.01, 0.02, 0.05, 0.1, 0.5, 1, 10]}}, "defaultTotalStake": {"type": "object", "example": {"calculated": false, "calculatedFromBase": true, "custom": false, "inherited": false, "value": 1}}, "maxTotalStake": {"type": "object", "example": {"calculated": true, "calculatedFromBase": true, "custom": false, "inherited": false, "value": 1000}}, "stakeDef": {"type": "object", "example": {"calculated": true, "calculatedFromBase": true, "custom": false, "inherited": false, "value": 1}}, "stakeMax": {"type": "object", "example": {"calculated": true, "calculatedFromBase": true, "custom": false, "inherited": false, "value": 10}}, "stakeMin": {"type": "object", "example": {"calculated": true, "calculatedFromBase": true, "custom": false, "inherited": false, "value": 0.01}}, "winMax": {"type": "object", "example": {"calculated": false, "calculatedFromBase": false, "custom": true, "inherited": false, "value": 1000}}}}, "GameLimitsConfiguration": {"allOf": [{"type": "object", "properties": {"id": {"type": "string", "example": "hu2s38S", "description": "Id of game limits configuration"}, "schemaConfigurationId": {"type": "string", "example": "g6Az90"}, "entityId": {"type": "string", "example": "g6Az90"}, "entityGameId": {"type": "string", "example": "g6Az90"}, "gameGroupId": {"type": "string", "example": "g6Az90"}, "segmentId": {"type": "string", "example": "g6Az90"}, "status": {"type": "string", "example": "active"}, "createdAt": {"type": "string", "description": "time when a record was created (ISO 8601 timestamp)", "example": "2016-02-23T12:45:42.324Z"}, "updatedAt": {"type": "string", "description": "last time a record was updated (ISO 8601 timestamp)", "example": "2017-03-08T05:15:54.213Z"}}}, {"$ref": "#/definitions/CreateGameLimitsConfiguration"}]}, "CreateGameLimitsConfiguration": {"allOf": [{"type": "object", "properties": {"schemaDefinitionId": {"type": "string", "example": "g6Az90"}, "gameCode": {"type": "string", "example": "sw_al"}, "gameGroupName": {"type": "string", "example": "VIP-1"}}}, {"$ref": "#/definitions/UpdateGameLimitsConfiguration"}]}, "UpdateGameLimitsConfiguration": {"type": "object", "properties": {"title": {"type": "string", "example": "Awesome configuration"}, "description": {"type": "string", "example": "My configuration is really awesome"}, "gameLimits": {"type": "object", "example": {"EUR": {"stakeAll": [0.1, 0.2, 0.3], "defaultTotalStake": 1, "winMax": 500000, "alert": 10, "block": 20}}}, "filters": {"type": "object", "example": {"EUR": {"minTotalBet": 100, "maxTotalBet": 200}}}, "levels": {"type": "array", "items": {"type": "string"}, "example": ["low", "high"], "description": "ability to customize levels for live games (for example remove mid level and keep only high and low)"}, "defaultLevel": {"type": "string", "example": "low", "description": "Default level for live games, property available only for customized levels in this game limits configuration"}}}, "DynamicDomainData": {"type": "object", "required": ["domain", "environment"], "properties": {"domain": {"type": "string", "description": "DNS name", "example": "gameserver.skywindgroup.com"}, "environment": {"type": "string", "description": "Environment name", "example": "gc"}, "description": {"type": "string", "description": "Optional descriptive information about the domain", "example": "Cloudfront static domain for Europe"}, "provider": {"type": "string", "description": "Optional service or content provider identifier", "example": "skywind"}, "status": {"type": "string", "enum": ["active", "suspended"], "description": "Domain status", "example": "active", "default": "active"}, "expiryDate": {"type": "string", "description": "Optional expiration date for the domain (ISO 8601 timestamp)", "example": "2025-12-31T23:59:59.999Z"}}}, "PlayersFilterData": {"type": "object", "properties": {"code__in": {"type": "string", "description": "list of player codes separated by commas", "example": "PL001,PL002"}, "firstName": {"description": "firstName equal to value", "example": "<PERSON>", "type": "string"}, "firstName__contains": {"description": "firstName contains string", "example": "an", "type": "string"}, "firstName__contains!": {"description": "firstName doesn't contain string", "example": "an", "type": "string"}, "firstName__in": {"description": "list of player first<PERSON><PERSON><PERSON> separated by commas", "example": "Ivan,Cheburek", "type": "string"}, "lastName": {"description": "lastName equal to value", "example": "<PERSON>", "type": "string"}, "lastName__contains": {"description": "lastName contains string", "example": "an", "type": "string"}, "lastName__contains!": {"description": "lastName doesn't contain string", "example": "an", "type": "string"}, "lastName__in": {"description": "list of player last<PERSON><PERSON><PERSON> separated by commas", "example": "Ivan,Cheburek", "type": "string"}, "email": {"description": "email equal to value", "example": "<EMAIL>", "type": "string"}, "email__contains": {"description": "email contains string", "example": "an", "type": "string"}, "email__contains!": {"description": "email doesn't contain string", "example": "an", "type": "string"}, "email__in": {"description": "list of player emails separated by commas", "example": "<PERSON>@test.com,<PERSON><PERSON><PERSON><PERSON>@test.com", "type": "string"}, "country": {"description": "country equal to value", "example": "US", "type": "string"}, "country__in": {"description": "list of player countries separated by commas", "example": "US,DE", "type": "string"}, "currency": {"description": "currency equal to value", "example": "USD", "type": "string"}, "currency__in": {"description": "list of player currencies separated by commas", "example": "USD,EUR", "type": "string"}, "lastLogin": {"type": "string", "description": "The last time a player logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "lastLogin__gt": {"type": "string", "description": "The last time a player logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "lastLogin__lt": {"type": "string", "description": "The last time a player logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt": {"type": "string", "description": "The time when a player was created (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt__gt": {"type": "string", "description": "The time when a player was created (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt__lt": {"type": "string", "description": "The time when a player was created (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "updatedAt": {"type": "string", "description": "The last time when a player was updated (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "updatedAt__gt": {"type": "string", "description": "The last time when a player was updated (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "updatedAt__lt": {"type": "string", "description": "The last time when a player was updated (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "isTest": {"type": "boolean", "description": "indicates if player is test", "example": false}}}, "AddPromoByPlayersCodesData": {"type": "object", "properties": {"playersCodes": {"type": "array", "items": {"type": "string"}, "example": ["PL001", "PL002"], "description": "array of player codes"}}}, "EntityBulkOperations": {"type": "array", "items": {"type": "object", "properties": {"action": {"type": "string", "enum": ["set", "reset"], "example": "set", "description": "Action of operation"}, "item": {"type": "object", "properties": {"id": {"type": "string", "example": "Hh89Kw3E", "description": "Proxy public ID"}, "type": {"type": "string", "enum": ["proxy", "static", "dynamic"], "example": "proxy", "description": "Type of operation item"}}}, "entityKey": {"type": "string", "example": "this-is-a-key", "description": "UUID entity code"}}}}, "PlayerBulkOperations": {"type": "array", "items": {"type": "object", "properties": {"action": {"type": "string", "enum": ["set", "reset"], "example": "set", "description": "Action of operation"}, "item": {"type": "object", "properties": {"id": {"type": "string", "example": "Hh89Kw3E", "description": "GameGroup public ID"}, "type": {"type": "string", "enum": ["group"], "example": "group", "description": "Type of operation item"}}}, "playerCode": {"type": "string", "example": "cheb<PERSON>ck"}, "entityKey": {"type": "string", "example": "this-is-a-key", "description": "UUID entity code"}}}}, "StaticDomainData": {"type": "object", "required": ["domain"], "properties": {"domain": {"type": "string", "description": "DNS name", "example": "gc.gaming.skywindgroup.com"}, "description": {"type": "string", "description": "Optional descriptive information about the domain", "example": "Cloudfront static domain for Europe"}, "provider": {"type": "string", "description": "Optional service or content provider identifier", "example": "skywind"}, "status": {"type": "string", "enum": ["active", "suspended"], "description": "Domain status", "example": "active", "default": "active"}, "type": {"type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"], "description": "Static domain type", "example": "static", "default": "static"}, "expiryDate": {"type": "string", "description": "Optional expiration date for the domain (ISO 8601 timestamp)", "example": "2025-12-31T23:59:59.999Z"}}}, "DomainInfo": {"type": "object", "properties": {"monitoringStatus": {"type": "object", "description": "Domain monitoring status by adapter", "additionalProperties": {"type": "object", "properties": {"accessStatus": {"type": "string", "enum": ["AVAILABLE", "BLOCKED", "UNKNOWN"], "description": "Domain access status", "example": "AVAILABLE"}, "lastCheckedAt": {"type": "string", "description": "Last check timestamp (ISO 8601 timestamp)", "example": "2025-01-17T10:30:00.000Z"}}, "required": ["accessStatus", "lastCheckedAt"]}, "example": {"tapking": {"accessStatus": "AVAILABLE", "lastCheckedAt": "2025-01-17T10:30:00.000Z"}}}}}, "StaticDomain": {"type": "object", "properties": {"id": {"type": "string", "description": "Domain id", "example": "Hh89Kw3E"}, "domain": {"type": "string", "description": "DNS name", "example": "gc.skywindgroup.com"}, "description": {"type": "string", "description": "Optional descriptive information about the domain", "example": "Cloudfront static domain for Europe"}, "provider": {"type": "string", "description": "Optional service or content provider identifier", "example": "skywind"}, "status": {"type": "string", "enum": ["active", "suspended"], "description": "Domain status", "example": "active"}, "type": {"type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"], "description": "Static domain type", "example": "static"}, "info": {"$ref": "#/definitions/DomainInfo", "description": "Optional domain information including monitoring status"}, "expiryDate": {"type": "string", "description": "Optional expiration date for the domain (ISO 8601 timestamp)", "example": "2025-12-31T23:59:59.999Z"}, "createdAt": {"type": "string", "description": "Creation time (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "Update time (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}}}, "DomainPoolData": {"type": "object", "properties": {"name": {"type": "string", "description": "The domain pool name", "example": "Pool123"}, "domainWatcherAdapterId": {"type": "string", "description": "Domain watcher adapter identifier", "example": "tapking"}, "domains": {"type": "array", "items": {"$ref": "#/definitions/DomainPoolItemData"}}}}, "DynamicDomainPoolData": {"type": "object", "properties": {"name": {"type": "string", "description": "The domain pool name", "example": "Pool123"}, "domainWatcherAdapterId": {"type": "string", "description": "Domain watcher adapter identifier", "example": "tapking"}, "domains": {"type": "array", "items": {"$ref": "#/definitions/DomainPoolItemData"}}}}, "DomainPoolItemData": {"type": "object", "properties": {"id": {"type": "string", "description": "The domain id", "example": "W4RkGRen"}, "isActive": {"type": "boolean", "description": "Flag that indicates whether the domain pool item is considered active or inactive", "example": true}}}, "StaticDomainPoolItem": {"allOf": [{"$ref": "#/definitions/StaticDomain"}, {"type": "object", "properties": {"isActive": {"type": "boolean", "description": "Flag that indicates whether the domain pool item is considered active or inactive", "example": true}, "blockedDate": {"type": "string", "description": "Optional timestamp when the domain was blocked (ISO 8601 timestamp)", "example": "2025-01-17T10:30:00.000Z"}}}]}, "DynamicDomainPoolItem": {"allOf": [{"$ref": "#/definitions/Domain"}, {"type": "object", "properties": {"isActive": {"type": "boolean", "description": "Flag that indicates whether the domain pool item is considered active or inactive", "example": true}, "blockedDate": {"type": "string", "description": "Optional timestamp when the domain was blocked (ISO 8601 timestamp)", "example": "2025-01-17T10:30:00.000Z"}}}]}, "StaticDomainPool": {"type": "object", "properties": {"id": {"type": "string", "description": "Encoded domain pool ID", "example": "W4RkGRen"}, "name": {"type": "string", "description": "The domain pool name", "example": "Pool123"}, "inherited": {"type": "boolean", "description": "Indicates whether the pool is inherited from a parent entity", "example": false}, "domainWatcherAdapterId": {"type": "string", "description": "Domain watcher adapter identifier", "example": "tapking"}, "createdAt": {"type": "string", "description": "Creation time (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "Update time (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "domains": {"type": "array", "items": {"$ref": "#/definitions/StaticDomainPoolItem"}}}}, "DynamicDomainPool": {"type": "object", "properties": {"id": {"type": "string", "description": "Encoded domain pool ID", "example": "W4RkGRen"}, "name": {"type": "string", "description": "The domain pool name", "example": "Pool123"}, "domainWatcherAdapterId": {"type": "string", "description": "Domain watcher adapter identifier", "example": "tapking"}, "createdAt": {"type": "string", "description": "Creation time (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "Update time (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "domains": {"type": "array", "items": {"$ref": "#/definitions/DynamicDomainPoolItem"}}}}, "Domain": {"allOf": [{"$ref": "#/definitions/StaticDomain"}, {"type": "object", "properties": {"environment": {"type": "string", "description": "Environment name", "example": "gc"}}}]}, "Proxy": {"type": "object", "required": ["url"], "properties": {"id": {"type": "string", "description": "Proxy public identifier", "example": "Hh89Kw3E"}, "url": {"type": "string", "description": "Proxy url", "example": "http://mega-proxy.com:8080/"}, "description": {"type": "string", "example": "Some description"}, "createdAt": {"type": "string", "description": "The time when a user is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time a user updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}}}, "RTPReport": {"type": "object", "required": ["entityId"], "properties": {"id": {"type": "string", "description": "Public identifier of rtp setting updates", "example": "Hh89Kw3E"}, "inheritedEntityId": {"type": "string", "description": "Public identifier of entity on which rtp settings were changed, can be inherited (will be present in response in case, when user has detailed view permissions)", "example": "Hh89Kw3E"}, "inheritedEntityTitle": {"type": "string", "description": "Entity title which changed rtp value (will be present in response in case, when user has detailed view permissions)", "example": "Entity title"}, "inherited": {"type": "boolean", "description": "Identifier which shows were rtp settings inherited or not (will be present in response in case, when user doesn't have detailed view permissions)", "example": true}, "entityId": {"type": "string", "description": "Public identifier of requested entity", "example": "Hh89Kw3E"}, "entityTitle": {"type": "string", "description": "Title of requested entity", "example": "Entity title"}, "gameId": {"type": "string", "description": "Public identifier of game on which rtp settings were changed", "example": "Hh89Kw3E"}, "gameTitle": {"type": "string", "description": "Game title on which rtp settings were changed", "example": "Hh89Kw3E"}, "gameCode": {"type": "string", "description": "Game code for which rtp settings were changed", "example": "sw_al"}, "finalRTP": {"type": "boolean", "description": "Final rtp value, includes rtp deduction is rtp rtp deduction is present and enabled", "example": 95.6}, "rtp": {"type": "number", "description": "Theoretical game rtp value", "example": 96.6}, "rtpDeduction": {"type": "number", "description": "Rtp deduction, percent that was deducted from a game RTP", "example": 1}, "ts": {"type": "string", "description": "The time when a rtp settings was changed", "example": "2016-12-10T12:45:32.324Z"}}}, "BriefEntity": {"allOf": [{"$ref": "#/definitions/Entity"}, {"type": "object", "properties": {"settings": {"$ref": "#/definitions/EntitySettings"}}}]}, "EntityDomainInfo": {"type": "object", "required": ["type", "name", "status", "key", "defaultCurrency", "defaultCountry", "defaultLanguage"], "properties": {"id": {"type": "string", "description": "encoded entity id", "example": "QsE3sR"}, "type": {"type": "string", "description": "type of item", "example": "entity"}, "name": {"type": "string", "description": "name", "example": "ENTITY1"}, "environment": {"type": "string", "description": "enviroment", "example": "gs1"}, "description": {"type": "string", "description": "description of item", "example": "Entity Description"}, "title": {"type": "string", "description": "Title of item", "example": "Main entity"}, "status": {"type": "string", "description": "status of item (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "key": {"type": "string", "description": "key for this item", "example": "A-KEY-FOR-THIS-ENTITY"}, "defaultCurrency": {"type": "string", "description": "default currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "defaultCountry": {"type": "string", "description": "default country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "defaultLanguage": {"type": "string", "description": "default language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "countries": {"type": "array", "description": "list of available countries codes [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "items": {"type": "string"}, "example": ["US", "CN"]}, "currencies": {"type": "array", "description": "list of available currencies codes [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "items": {"type": "string"}, "example": ["USD", "CNY"]}, "languages": {"type": "array", "description": "list of available languages codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "items": {"type": "string"}, "example": ["en", "zh"]}, "isMerchant": {"type": "boolean", "description": "indicates if brand is merchant", "example": false}, "isTest": {"type": "boolean", "description": "indicates if entity is test", "example": false}, "domains": {"type": "array", "description": "domains from which users can login to BO", "items": {"type": "string"}, "example": ["bo.gc.skywind-tech.com", "bo2.gc.skywind-tech.com"]}, "parentDomains": {"type": "array", "description": "Parent domains from which users can login to BO", "items": {"type": "string"}, "example": ["bo.gc.skywind-tech.com", "bo2.gc.skywind-tech.com"]}, "staticDomainTags": {"type": "array", "description": "Static domain tags (case insensitive)", "items": {"type": "string"}, "example": ["skywind.com"]}, "dynamicDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "staticDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "lobbyDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "liveStreamingDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "ehubDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "staticDomainPoolId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "dynamicDomainPoolId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "DeploymentGroup": {"$ref": "#/definitions/DeploymentGroup"}, "merchantTypes": {"type": "array", "description": "list of available merchant types for entity", "items": {"type": "string"}, "example": ["ipm", "mrch_json", "pop_moorgate"]}}}, "Entity": {"allOf": [{"$ref": "#/definitions/EntityDomainInfo"}, {"type": "object", "properties": {"path": {"type": "string", "description": "path of this item", "example": "MASTER:TOP"}, "child": {"type": "array", "items": {"$ref": "#/definitions/Entity"}}}}]}, "EntityWithProxy": {"allOf": [{"$ref": "#/definitions/Entity"}, {"type": "object", "properties": {"proxy": {"$ref": "#/definitions/Proxy"}}}]}, "EntityShortInfo": {"type": "object", "required": ["path"], "properties": {"id": {"type": "string", "description": "encoded id of entity", "example": "Mh8WSZ"}, "path": {"type": "string", "description": "path of this item", "example": "MASTER:TOP"}, "child": {"type": "array", "description": "entity childs info", "items": {"$ref": "#/definitions/EntityShortInfo"}}, "name": {"type": "string", "description": "Name of the entity", "example": "ENT1"}, "title": {"type": "string", "description": "Title of the entity", "example": "ENT_TITLE"}, "type": {"type": "string", "description": "Type of the entity", "example": "brand"}, "description": {"type": "string", "description": "description of item", "example": "Entity Description"}, "status": {"type": "string", "description": "status of item (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "key": {"type": "string", "description": "key for this item", "example": "A-KEY-FOR-THIS-ENTITY"}, "defaultCurrency": {"type": "string", "description": "default currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "defaultCountry": {"type": "string", "description": "default country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "defaultLanguage": {"type": "string", "description": "default language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "dynamicDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "staticDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "lobbyDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "liveStreamingDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "ehubDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "staticDomainPoolId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "dynamicDomainPoolId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "environment": {"type": "string", "example": "gs", "description": "Environment ID"}, "jurisdiction": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "title": {"type": "string"}}}, "example": [{"code": "FI", "title": "Finland Jurisdiction"}, {"code": "VU", "title": "Vanuatu Jurisdiction"}, {"code": "DE", "title": "Germany jurisdiction"}], "description": "This field is optional and is triggered by includeJurisdiction flag, will return array with jurisdictions."}}}, "EntityWithPath": {"type": "object", "properties": {"id": {"type": "string", "description": "encoded id of entity", "example": "Mh8WSZ"}, "fullPath": {"type": "string", "description": "path of this item", "example": "MASTER:TOP"}}}, "SearchByKey": {"type": "object", "properties": {"key": {"type": "string", "description": "entity key", "example": "A-KEY-FOR-THIS-ENTITY"}, "child": {"type": "object", "description": "Child reference", "$ref": "#/definitions/SearchByKey"}}}, "EntityWithBalances": {"allOf": [{"$ref": "#/definitions/Entity"}, {"type": "object", "properties": {"balances": {"$ref": "#/definitions/Balances"}}}]}, "BrandOrMerchant": {"type": "object", "required": ["type", "name", "status", "key", "defaultCurrency", "defaultCountry", "defaultLanguage"], "properties": {"id": {"type": "string", "description": "id", "example": "pQ3513OE"}, "type": {"type": "string", "description": "type of item", "example": "entity"}, "name": {"type": "string", "description": "name", "example": "ENTITY1"}, "description": {"type": "string", "description": "description of item", "example": "Entity Description"}, "title": {"type": "string", "description": "Title of item", "example": "Main entity"}, "status": {"type": "string", "description": "status of item (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "key": {"type": "string", "description": "key for this item", "example": "A-KEY-FOR-THIS-ENTITY"}, "defaultCurrency": {"type": "string", "description": "default currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "defaultCountry": {"type": "string", "description": "default country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "defaultLanguage": {"type": "string", "description": "default language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "countries": {"type": "array", "description": "list of available countries codes [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "items": {"type": "string"}, "example": ["US", "CN"]}, "currencies": {"type": "array", "description": "list of available currencies codes [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "items": {"type": "string"}, "example": ["USD", "CNY"]}, "languages": {"type": "array", "description": "list of available languages codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "items": {"type": "string"}, "example": ["en", "zh"]}, "path": {"type": "string", "description": "path of this item", "example": "MASTER:TOP"}, "isMerchant": {"type": "boolean", "description": "indicates if brand is merchant", "example": false}, "domain": {"type": "string", "description": "domain from which users can login to BO", "example": "bo.gc.skywind-tech.com"}, "dynamicDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "staticDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "lobbyDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "liveStreamingDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "ehubDomainId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "staticDomainPoolId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}, "dynamicDomainPoolId": {"type": "string", "example": "R4aKADKB", "description": "Public ID"}}}, "BrandOrMerchantWithBalances": {"allOf": [{"$ref": "#/definitions/BrandOrMerchant"}, {"type": "object", "properties": {"balances": {"$ref": "#/definitions/Balances"}}}]}, "EntityPaymentInfo": {"type": "object", "required": ["currency", "amount", "ts", "isTest"], "properties": {"currency": {"$ref": "#/definitions/CurrencyCode"}, "amount": {"$ref": "#/definitions/Amount"}, "ts": {"$ref": "#/definitions/TS"}, "isTest": {"$ref": "#/definitions/isTest"}, "initiatorName": {"type": "string", "description": "Initiator login(code)", "example": "admin"}}}, "EntityDebitsPaymentInfo": {"allOf": [{"$ref": "#/definitions/EntityPaymentInfo"}, {"type": "object", "properties": {"fromEntityInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of entity", "example": "Hh89Kw3E"}, "name": {"type": "string", "description": "entity name", "example": "parent-entity"}, "title": {"type": "string", "description": "entity title", "example": "Casino parent entity"}, "path": {"type": "string", "description": "entity path", "example": ":TLE1:ENT1"}}}}}]}, "EntityCreditsPaymentInfo": {"allOf": [{"$ref": "#/definitions/EntityPaymentInfo"}, {"type": "object", "properties": {"toEntityInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of entity", "example": "Hh89Kw3E"}, "name": {"type": "string", "description": "entity name", "example": "parent-entity"}, "title": {"type": "string", "description": "entity title", "example": "Casino parent entity"}, "path": {"type": "string", "description": "entity path", "example": ":TLE1:ENT1"}}}}}]}, "EntityCreditsDebitsPaymentInfo": {"allOf": [{"$ref": "#/definitions/EntityCreditsPaymentInfo"}, {"type": "object", "properties": {"fromEntityInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of entity", "example": "Hh89Kw3E"}, "name": {"type": "string", "description": "entity name", "example": "parent-entity"}, "title": {"type": "string", "description": "entity title", "example": "Casino parent entity"}, "path": {"type": "string", "description": "entity path", "example": ":TLE1:ENT1"}}}}}]}, "EntityCredits": {"type": "array", "items": {"$ref": "#/definitions/EntityCreditsPaymentInfo"}}, "EntityDebits": {"type": "array", "items": {"$ref": "#/definitions/EntityDebitsPaymentInfo"}}, "EntityCreditsDebits": {"type": "array", "items": {"$ref": "#/definitions/EntityCreditsDebitsPaymentInfo"}}, "MerchantEntityWithBalances": {"allOf": [{"$ref": "#/definitions/BrandOrMerchant"}, {"type": "object", "properties": {"merchant": {"$ref": "#/definitions/Merchant"}, "balances": {"$ref": "#/definitions/Balances"}, "jurisdictionCode": {"type": "string", "example": "MT", "description": "Jurisdiction code"}}}]}, "MerchantEntity": {"allOf": [{"$ref": "#/definitions/BrandOrMerchant"}, {"type": "object", "properties": {"merchant": {"$ref": "#/definitions/Merchant"}}}]}, "TransactionInfo": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "description": "Transaction status [absent | processing | committed]", "example": "committed"}, "playerBalanceAfter": {"type": "number", "description": "Player balance after transaction", "example": 100}}}, "Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 60}, "message": {"type": "string", "description": "error message", "example": "Entity already exists"}}}, "Permissions": {"type": "array", "items": {"type": "string"}, "example": ["user", "entity:view"]}, "CaptchaInfo": {"type": "object", "properties": {"csrfToken": {"type": "string", "description": "Encrypted captcha value", "example": "bOYRB59zKWLuZzPink8IJxnOUQ+SU/etoyFa0YfEd9qPQfFzuvzI+OyiR8BuDgr7tbpPZELMyfA5BtZ61dqSRTwuhLnBvIXckrvfOFPMTPIXD3afrbW9yH3Jg9mjOrVqjzMPU5tL8pz/arMq+H2M/EPEK1GuT1t2sVNLkML/nGg2W72w8r/fD51+Cw=="}, "image": {"type": "string", "description": "Binary image value", "example": "data:image/gif;base64,R0lGODlhyABGAIMAAP/rO//rO//rO//rO//rO//rO//rO//rO//rO//rO//rO//rO//rO//rO//rO////ywAAAAAyABGAAAE+vDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3z68MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPrwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx98+vDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3z68MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPDBBx988MEHH3zwwQcffPrwwQcffPDBBx988MEHH.."}}}, "BiReportInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of report", "example": "4gGt2ks0"}, "reportId": {"type": "string", "description": "short report id unique for the same report in different languages", "example": "ggr-daily"}, "name": {"type": "string", "description": "short report name or abbreviation", "example": "winsplayer"}, "caption": {"type": "string", "description": "caption of report", "example": "Players Wins"}, "description": {"type": "string", "description": "description of report", "example": "The biggest wins by players"}, "permission": {"type": "string", "description": "postfix for report permission", "example": "player"}, "workbook": {"type": "string", "description": "name of Business Intelligence workbook with reports", "example": "countries"}, "reportGroup": {"type": "string", "description": "name of Business Intelligence group for reports", "example": "Performance"}, "status": {"type": "string", "description": "status of report suspended or normal (by default)", "example": "normal"}, "createdAt": {"type": "string", "description": "The time when a report is posted (ISO 8601 timestamp)", "example": "2018-02-28T11:37:12.754Z"}, "settings": {"type": "object", "description": "Custom data of Report", "example": {"height": 1600, "language": "zh-cn", "reportId": "ggr-daily"}}, "ordering": {"type": "number", "example": 1}}}, "BiReportUpdateOrderingData": {"type": "object", "required": ["newPosition"], "properties": {"newPosition": {"type": "number", "description": "New position of report, starts from 0", "example": 0}}}, "BiReportDomains": {"type": "object", "properties": {"pid": {"type": "string", "description": "Public id"}, "trustServerUrl": {"type": "string", "description": "Domain that is called to generate trust ticket", "example": "https://bi.gcpstg.m27613.com/trusted"}, "baseUrl": {"type": "string", "description": "Domain that is used to open bi report", "example": "https://bi.gcpstg.m27613.com/#/site/cd2"}, "isSelected": {"type": "boolean", "description": "A flag which means the set of domains is selected for generating reports", "example": "true"}, "createdAt": {"type": "string", "description": "Date when the set of domains is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "Date when the set of domains is updated (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}}}, "BiReportDomainsCreateData": {"type": "object", "required": ["trustServerUrl", "baseUrl"], "properties": {"trustServerUrl": {"type": "string", "description": "Domain that is called to generate trust ticket", "example": "https://bi.gcpstg.m27613.com/trusted"}, "baseUrl": {"type": "string", "description": "Domain that is used to open bi report", "example": "https://bi.gcpstg.m27613.com/#/site/cd2"}}}, "BiReportDomainsUpdateData": {"type": "object", "properties": {"trustServerUrl": {"type": "string", "description": "Domain that is called to generate trust ticket", "example": "https://bi.gcpstg.m27613.com/trusted"}, "baseUrl": {"type": "string", "description": "Domain that is used to open bi report", "example": "https://bi.gcpstg.m27613.com/#/site/cd2"}}}, "BiPermissionInfo": {"type": "object", "properties": {"postfix": {"type": "string", "description": "postfix of permission", "example": "ggr"}, "permission": {"type": "string", "description": "permission", "example": "keyentity:bi:report:ggr"}, "description": {"type": "string", "description": "brief annotation", "example": "View Business Intelligence ggr reports"}}}, "BiReportUpdateData": {"type": "object", "properties": {"caption": {"type": "string", "description": "caption of report", "example": "Players to Games"}, "description": {"type": "string", "description": "description of report", "example": "The most valuable players for games"}, "permission": {"type": "string", "description": "postfix for report permission", "example": "player"}, "status": {"type": "string", "description": "status of report suspended or normal", "example": "normal"}, "settings": {"type": "object", "description": "Custom data of Report", "example": {"height": 1600, "language": "zh-cn", "reportId": "ggr-daily", "trustServerURL": "https://trust-server-url.com", "baseUrl": "https://base-url.com", "tokenExpiresIn": 240}}}}, "BiReportCreateData": {"type": "object", "properties": {"name": {"type": "string", "description": "short report name or abbreviation", "example": "winsplayer"}, "caption": {"type": "string", "description": "caption of report", "example": "Players Wins"}, "description": {"type": "string", "description": "description of report", "example": "The biggest wins by players"}, "permission": {"type": "string", "description": "postfix for report permission (the same like name by default)", "example": "player"}, "workbook": {"type": "string", "description": "name of Business Intelligence workbook with reports", "example": "countries"}, "status": {"type": "string", "description": "status of report suspended or normal (by default)", "example": "normal"}, "settings": {"type": "object", "description": "Custom data of Report", "example": {"height": 1600, "language": "zh-cn", "reportId": "ggr-daily", "trustServerURL": "https://trust-server-url.com", "baseUrl": "https://base-url.com", "tokenExpiresIn": 240}}}}, "BiCreateUrlData": {"type": "object", "properties": {"reportId": {"type": "string", "description": "short report id unique for the same report in different languages", "example": "ggr-daily"}, "tournamentId": {"type": "string", "description": "Tournament id", "example": "id"}}}, "BiUrlInfo": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of embedded Business Intelligence Report", "example": "http://external-report.games.vikara.in/views/players/daily?token=77340b3c-0a87-11e8-ba89&embedded=true"}, "baseUrl": {"type": "string", "description": "Base URL of Business Intelligence Server", "example": "http://external-report.games.vikara.in/views"}, "name": {"type": "string", "description": "short report name or abbreviation", "example": "daily"}, "workbook": {"type": "string", "description": "name of Business Intelligence workbook with reports", "example": "players"}, "token": {"type": "string", "description": "access token of Business Intelligence Report", "example": "77340b3c-0a87-11e8-ba89"}, "expiredAt": {"type": "string", "description": "access token expired at (timestamp without timezone)", "example": "2018-03-11T04:02:11.562Z"}, "settings": {"type": "object", "description": "Custom data of Report", "example": {"height": 1600, "language": "zh-cn", "reportId": "ggr-daily"}}}}, "PlayerInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "id": {"type": "string", "description": "public id of player", "example": "481eAS0d"}, "status": {"type": "string", "description": "player status (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "firstName": {"type": "string", "description": "player first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "player last name", "example": "Dow"}, "nickname": {"type": "string", "description": "player nickname", "example": "<PERSON><PERSON><PERSON>"}, "email": {"type": "string", "description": "email address", "example": "<EMAIL>"}, "country": {"type": "string", "description": "country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "currency": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "language": {"type": "string", "description": "language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "customData": {"type": "object", "description": "custom player's data", "example": [{"key": "league", "keyName": "faction", "value": "Noasauridae"}]}, "isTest": {"type": "boolean", "description": "is player created only for testing", "example": false}, "lastLogin": {"type": "string", "description": "The last time a user logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt": {"type": "string", "description": "The time when a user is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time a user updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "deactivatedAt": {"type": "string", "description": "Datetime when player should be deactivated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}, "brandTitle": {"type": "string", "description": "title of brand", "example": "<PERSON><PERSON> Lett Win"}, "agentId": {"type": "string", "description": "agent's public id", "example": "jdG8Sem9"}, "agentTitle": {"type": "string", "description": "agent's title", "example": "brother<PERSON>ite"}, "agentDomain": {"type": "string", "description": "agent's domain", "example": "example.com"}, "comments": {"type": "string", "description": "Customers are be able to same any text information there", "example": "Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."}, "isVip": {"type": "boolean", "description": "VIP player or not", "example": false}}}, "BlockedPlayerInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "status": {"type": "string", "description": "player status (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}}}, "MerchantTestPlayerInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}, "startDate": {"type": "string", "description": "start date", "example": "2020-12-21"}, "endDate": {"type": "string", "description": "end date", "example": "2020-12-30"}, "source": {"type": "string", "description": "source: integration or support", "example": "integration"}}}, "CreateMerchantTestPlayer": {"type": "object", "required": ["code"], "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "startDate": {"type": "string", "description": "start date", "example": "2020-12-21"}, "endDate": {"type": "string", "description": "end date", "example": "2020-12-30"}}}, "UpdateMerchantTestPlayer": {"type": "object", "required": ["code", "endDate"], "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "endDate": {"type": "string", "description": "end date", "example": "2020-12-30"}}}, "BlockedPlayerInfoWithAudit": {"allOf": [{"$ref": "#/definitions/BlockedPlayerInfo"}, {"type": "object", "properties": {"auditData": {"type": "array", "items": {"$ref": "#/definitions/AuditSchema"}}}}]}, "PlayerRewards": {"type": "object", "properties": {"bonusCoins": {"type": "array", "items": {"allOf": [{"$ref": "#/definitions/PlayerBonusCoinReward"}, {"type": "object", "properties": {"promoId": {"type": "string", "description": "Promotion id", "example": "x6hsdf"}}}]}}, "freeBets": {"type": "array", "items": {"allOf": [{"$ref": "#/definitions/PlayerFreebetReward"}, {"type": "object", "properties": {"promoId": {"type": "string", "description": "Promotion id", "example": "x6hsdf"}}}]}}}}, "PlayerInfoWithBalances": {"type": "object", "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "id": {"type": "string", "description": "public id of player", "example": "481eAS0d"}, "status": {"type": "string", "description": "player status (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "firstName": {"type": "string", "description": "player first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "player last name", "example": "Dow"}, "nickname": {"type": "string", "description": "player nickname", "example": "<PERSON><PERSON><PERSON>"}, "email": {"type": "string", "description": "email address", "example": "<EMAIL>"}, "country": {"type": "string", "description": "country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "currency": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "language": {"type": "string", "description": "language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "customData": {"type": "object", "description": "custom player's data", "example": [{"key": "league", "keyName": "faction", "value": "Noasauridae"}]}, "isTest": {"type": "boolean", "description": "is player created only for testing", "example": false}, "lastLogin": {"type": "string", "description": "The last time a user logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt": {"type": "string", "description": "The time when a user is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time a user updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}, "brandTitle": {"type": "string", "description": "title of brand", "example": "<PERSON><PERSON> Lett Win"}, "agentId": {"type": "string", "description": "agent's public id", "example": "jdG8Sem9"}, "agentTitle": {"type": "string", "description": "agent's title", "example": "brother<PERSON>ite"}, "agentDomain": {"type": "string", "description": "agent's domain", "example": "example.com"}, "balances": {"$ref": "#/definitions/Balances"}, "gameGroup": {"type": "string", "description": "player's game group", "example": "VIP-1"}, "defaultGameGroup": {"type": "string", "description": "default game group, which are used if player doesn't have game group", "example": "VIP-default"}, "rewards": {"$ref": "#/definitions/PlayerRewards"}, "isOnline": {"type": "boolean", "description": "Online / Offline player status", "example": false}, "isBlocked": {"type": "boolean", "description": "If true then player is blocked, default false", "example": false}, "isVip": {"type": "boolean", "description": "VIP player or not", "example": false}}}, "PlayerChatInfo": {"type": "object", "properties": {"brandId": {"type": "string", "description": "player brand id", "example": "5"}, "brandTitle": {"type": "string", "description": "Brand name or title", "example": "Test"}, "nickname": {"type": "string", "description": "player nickname", "example": "<PERSON><PERSON><PERSON>"}, "isVip": {"type": "boolean", "description": "VIP player or not", "example": false}, "playerCode": {"type": "string", "description": "player code", "example": "PL0001"}, "isTracked": {"type": "boolean", "description": "flag which indicates if this is a tracked playe", "example": false}, "isPublicChatBlock": {"type": "boolean", "description": "flag which indicates if this player should be blocked for sending chat messages.", "example": false}, "isPrivateChatBlock": {"type": "boolean", "description": "flag which indicates if this player should be blocked for sending chat messages.", "example": false}, "hasWarn": {"type": "boolean", "description": "flag which indicates if this player is warn", "example": false}, "nicknameChangeAttempts": {"type": "number", "description": "Shows how many attempts a player done to change his/her nickname", "example": 1}, "restrictedIpCountries": {"$ref": "#/definitions/RestrictedIpCountries"}}}, "PlayerChatInfoExtended": {"allOf": [{"$ref": "#/definitions/PlayerChatInfo"}, {"type": "object", "properties": {"brandTitle": {"type": "string", "description": "brand title", "example": "Brand Title"}, "firstName": {"type": "string", "description": "Player first Name", "example": "first name"}, "lastName": {"type": "string", "description": "Player last Name", "example": "last name"}}}]}, "PlayerGameGroup": {"type": "object", "description": "", "properties": {"playerCode": {"type": "string", "description": "player code", "example": "PL0001"}, "gameGroup": {"type": "string", "description": "game group", "example": "gameGroup1"}}, "example": [{"playerCode": "player132", "gameGroup": "gameGroup1"}]}, "PlayerGameGroups": {"type": "array", "items": {"$ref": "#/definitions/PlayerGameGroup"}, "description": "player game group"}, "PlayerInfoWithBalanceExtended": {"allOf": [{"$ref": "#/definitions/PlayerInfoWithBalances"}, {"type": "object", "properties": {"lastAction": {"type": "string", "description": "The last time a user made a spin (ISO 8601 timestamp)", "example": "2018-10-10T13:13:18.105Z"}, "isBlocked": {"type": "boolean", "description": "If true then player is blocked, default false", "example": false}}}]}, "PlayerInfoExtended": {"allOf": [{"$ref": "#/definitions/PlayerInfo"}, {"type": "object", "properties": {"gameGroup": {"type": "string", "description": "player's game group", "example": "VIP"}}}]}, "PlayerSessionInfo": {"type": "object", "required": ["ttl"], "properties": {"ttl": {"type": "integer", "description": "Session time-to-live (seconds)"}}}, "TemporaryPassword": {"type": "object", "properties": {"temporaryPassword": {"type": "string", "description": "temporary password", "example": "GL7-os2"}}}, "EntityWhitelist": {"type": "array", "items": {"type": "string"}, "description": "list of whitelisted IPs", "example": ["10.7.1.*", "************"]}, "EntityWhitelistDetailed": {"type": "object", "properties": {"own": {"type": "array", "items": {"type": "string"}, "description": "list of whitelisted IPs for entity"}, "parent": {"type": "array", "items": {"type": "string"}, "description": "list of whitelisted IPs for parent entities"}}, "description": "list of whitelisted IPs", "example": {"own": ["*********", "*********"], "parent": ["127.0.0.1", "*********"]}}, "EntitySettings": {"type": "object", "required": ["emailTemplates"], "properties": {"defaultBrandPublicId": {"type": "string", "description": "Public brand id", "example": "Hh89Kw3E"}, "emailTemplates": {"type": "object", "description": "email templates", "example": {"passwordRecovery": {"from": "Skywind <<EMAIL>>", "subject": "Skywind Password Reset", "html": "\n{{username}},\n<p>\nTo reset your password, please use the following link: <a href=\"{{domain}}/auth/resetpassword?token={{token}}&username={{username}}&secretKey={{secretKey}}\">click to reset\n</a>.\nIt will expire in 15 minutes — after that you'll need to request a new one.\n</p>\n<p>\nIf you didn't request this change, please let us know by replying to this email.\n</p>\n", "options": {"resetBaseUrl": "{{domain}}/v1/login/password/confirm"}}}}, "defaultFunGameBalance": {"type": "number", "description": "Default balance for fun game mode", "example": 1000}, "roundExpireAt": {"type": "number", "description": "Round expiration time (in minutes)", "example": 360}, "ghAppSettings": {"type": "object", "description": "Game History App settings", "example": {"urlParams": {"showAdditionalInfo": true}}}, "restrictedCountries": {"type": "array", "items": {"type": "string"}, "description": "List of restricted countries", "example": ["RU", "BY"]}, "restrictions": {"type": "object", "description": "restrictions to launch games, e.g. allowed country/currency", "example": {"countries": {"US": ["USD"], "CN": ["CNY", "USD"]}, "ignore": {"type": "boolean", "description": "ignore country-currency restrictions", "example": true}}}, "ipWhitelist": {"type": "array", "items": {"type": "string"}, "description": "list of whitelisted IPs", "example": ["10.4.*.*", "127.0.0.1"]}, "boIpWhitelist": {"type": "array", "items": {"type": "string"}, "description": "list of whitelisted IPs for BO", "example": ["10.4.*.*", "127.0.0.1"]}, "urlParams": {"type": "object", "description": "Parameters to append to game url. Available options: splash, cashier, lobby, history_url.", "example": {"splash": "sw_pt", "cashier": 1, "lobby": 0, "history_url": "url", "history2_url": "url", "socketVersion": "4"}}, "gameSplashes": {"type": "object", "description": "Parameters to append to game url. Special chars get encoded. urlParams will overwrite same existing params.", "example": {"sw_csgo": "sw_pt", "sw_pubg": "sw_pg"}}, "maxTestPlayers": {"type": "integer", "description": "Maximum number of test players allowed for this entity", "example": 25}, "passwordPattern": {"type": "string", "description": "Password pattern for validation", "example": "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)^[a-zA-Z0-9!@#$%^&*()_\\~\\-\\`\\\\/\\\"\\'+|\\[\\]}{:;'?/>.<,]{8,255}$"}, "isAccountBlockingEnabled": {"type": "boolean", "description": "True if an account should be blocked based on its balance", "example": false}, "supportedBonusPaymentMethod": {"type": "string", "enum": ["credit", "bonus", "manual"], "example": "credit", "description": "Supported ways to pay a bonuses for tournaments, split prize, etc."}, "newDeferredPaymentPromoId": {"type": "boolean", "description": "True if need to use promo id from phantom", "example": false}, "limitFeaturesToMaxTotalStake": {"$ref": "#/definitions/LimitFeaturesToMaxTotalStake"}, "launchGameInsideLobby": {"type": "boolean", "description": "Allow to launch game inside lobby if default lobby is define", "example": false}, "twoFactorAuthSettings": {"type": "object", "description": "Two factor auth settings", "example": {"isAuthEnabled": true, "authOptions": ["sms", "email", "google"], "mailTemplates": {"default": {"from": "Skywind", "subject": "Login code", "html": "{{username}}, here is your <b>{{authCode}}</b> auth code"}, "en": {"from": "Skywind", "subject": "Login code", "html": "{{username}}, here is your <b>{{authCode}}</b> auth code"}, "cn": {"from": "Skywind", "subject": "Login code (ch)", "html": "{{username}}, here is your <b>{{authCode}}</b> auth code (ch)"}}, "smsTemplates": {"default": "{{username}}, here is your sms <b>{{authCode}}</b> auth code", "cn": "{{username}}, here is your <b>{{authCode}}</b> sms auth code (ch)"}}}, "maintenanceUrl": {"type": "string", "description": "Url that will be returned to players instead of game url if entity is under maintenance", "example": "https://flc.cdnsky.net/maintenance/"}, "isPlayerPasswordChangeEnabled": {"type": "boolean", "description": "True if player's password should be changed on the first login", "example": false}, "playerPrefixEnabled": {"type": "boolean", "description": "Enable/Disable player prefix", "example": false}, "hideBalanceBeforeAndAfter": {"type": "boolean", "description": "True if balance before and after should be hidden in reports", "example": false}, "skywindHistory": {"type": "boolean", "description": "True if skywind history should be in priority", "example": false}, "isPasswordChangeEnabled": {"type": "boolean", "description": "True if user's password should be changed on the first login", "example": false}, "isPasswordForceChangeEnabled": {"type": "boolean", "description": "True if user's password should be expired after specified amount of time", "example": false}, "addDecodedRoundIdToHistory": {"type": "boolean", "description": "True if external/internal history api should includes decoded round id (Seamless)", "example": false}, "passwordForceChangePeriod": {"type": "integer", "description": "Number of days password expires in, used if isPasswordForceChangeEnabled set to true", "example": 90}, "virtualCurrencyRate": {"type": "object", "description": "Exchange rates for virtual currencies", "example": {"BNS": {"USD": 0.1, "EUR": 0.09}}}, "logoutControl": {"type": "object", "description": "Logout control for integration", "example": {"ignorePayments": {"gameClosure": true, "gameRelaunch": true, "offlineRetry": true}}}, "bonusPlayEntity": {"type": "boolean", "description": "Shows that entity as the one for playing operator's bonus money.", "example": false}, "responsibleGaming": {"type": "object", "description": "Entity responsible gaming settings", "example": {"enabled": true, "jurisdiction": "UK"}}, "validationSettings": {"type": "object", "properties": {"playerCodeLength": {"type": "object", "example": {"min": 5, "max": 10}}}}, "storePlayerInfo": {"type": "boolean", "description": "True if need to store player personal information", "example": false}, "isBetaBO": {"type": "boolean", "description": "True if need to use new UBO", "example": false}, "playerPrefix": {"type": "string", "description": "prefix for player code", "example": "ABC_"}, "gamble": {"type": "boolean", "description": "Enable gamble feature (no value means Enabled by default)", "example": false}, "contributionPrecision": {"type": "number", "description": "Value precision: Number of digits after dot.", "example": 5}, "splitPayment": {"type": "boolean", "description": "Indicates that games transactions bet + win must be split on 2 phases bet and win.", "example": false}, "deferredContribution": {"type": "boolean", "description": "Should be true if you need contribution amount before bet payment Works only with enabled split payment.", "example": false}, "maxPaymentRetryAttempts": {"type": "number", "description": "Indicates how many retries should be made by game server", "example": 12}, "minPaymentRetryTimeout": {"type": "number", "description": "Indicates when to start retrying payment  (seconds). Must be greater or equals to 30", "example": 30}, "isPlayerCodeUniqueInSubtree": {"type": "boolean", "description": "Indicates that player code shall be unique under the whole structure", "example": false}, "uniqueEntityNamesInSubtree": {"type": "boolean", "description": "Indicates that entity name shall be unique under the whole structure", "example": false}, "uniqueUsernamesInSubtree": {"type": "boolean", "description": "Indicates that username shall be unique under the whole structure", "example": false}, "autoAddNewGamesToGroups": {"type": "boolean", "description": "If true new games will be added automatically to existing game groups", "example": false}, "dynamicRoutingEnabled": {"type": "boolean", "description": "Indicates that dynamic domain will be selected per player location", "example": false}, "autoPlaySettings": {"type": "array", "items": {"$ref": "#/definitions/AutoPlaySettings"}, "example": [{"label": "10", "value": 10}, {"label": "25", "value": 25}, {"label": "50", "value": 50}, {"label": "99", "value": 99}, {"label": "UNTIL FEATURE", "value": 0, "isUntilFeature": true, "isDefault": true}]}, "jpTickerRefreshPeriod": {"type": "number", "description": "Jackpot ticker refresh period in seconds", "example": 15}, "rtpDeduction": {"type": "number", "description": "RTP deduction percent", "example": 1}, "rtpDeductionFunModeEnabled": {"type": "boolean", "description": "Indicates if RTP deduction is enabled in 'fun' mode", "example": false}, "marketing": {"type": "object", "description": "configuration of contributions to marketing jackpots", "example": {"contributions": [{"jackpotId": "MRKT-JP", "contribution": 0.5}]}}, "newLimitsEnabled": {"type": "boolean", "description": "Indicates whether new limits are enabled", "example": false}, "replayEnabled": {"type": "boolean", "description": "Enables returning the replay url in rounds history, if supported by the game", "example": false}, "isMarketplaceSupported": {"type": "boolean", "description": "Indicates whether games of entity supports marketplace", "example": false}, "markPlayersAsTest": {"type": "boolean", "description": "Mark all players as test if set to true", "example": false}, "gameGroupsInheritance": {"type": "boolean", "description": "Indicate should game groups support inheritance by tree (for old limits system)", "example": false}, "clientFeatures": {"$ref": "#/definitions/ClientFeatures"}, "allowToOverrideDefaultLimits": {"type": "boolean", "description": "If flag enabled validation of game limits partially disabled", "example": true}, "blockGameLaunchForEmptyReferrer": {"type": "boolean", "description": "Used for website whitelisted check, by default 'true'", "example": true}, "regulatoryLinks": {"type": "array", "description": "Regulatory links", "items": {"$ref": "#/definitions/RegulatoryLink"}}, "minBonusCoinBet": {"type": "number", "description": "Min Bonus Coin balance to allow adding a new promotion to the player", "example": 10}, "defaultGameGroup": {"type": "string", "description": "Name of default game group", "example": "VIP-1"}, "autoCreateTestJackpot": {"type": "boolean", "description": "Auto create test jackpot instances", "example": false}, "allowedStaticDomainsForChildId": {"type": "array", "description": "Allowed static domains for child entities", "items": {"type": "string", "description": "Domain id", "example": "Hh89Kw3E"}}, "denyBoSimultaneousLogin": {"type": "boolean", "description": "Deny simultaneous login on different devices for BO user", "example": false}, "finalizationSupport": {"$ref": "#/definitions/BrandFinalizationType"}, "omitBnsBalance": {"type": "boolean", "description": "If we need to omit entity BNS balance check. False means we need to check and assign BNS balance from an entity to a player. True means we just assign BNS to a player without checking and updating the entity BNS balance.", "example": false}, "useMerchantPlayerGameGroup": {"type": "boolean", "description": "Indicates if merchant players can be assigned to a game group directly by using operator API", "example": false}, "disableTransferOutForOnlinePlayers": {"type": "boolean", "description": "Disables transfer outs for online players", "example": false}, "skipPendingPaymentReAuthentication": {"type": "boolean", "description": "Flag that allows to skip payment re-authentication on init if the operator requests it - meaning to use the original token", "example": false}, "addBetAmountOnFreeBetRollback": {"type": "boolean", "description": "Flag that enables adding the bet amount on free bet rollback operations", "example": true}, "bonusPaymentMethod": {"type": "string", "enum": ["credit", "bonus", "manual"], "example": "credit", "description": "The way to pay a bonuses for tournaments, split prize, etc."}, "ggrCalculation": {"type": "string", "enum": ["wallet", "round"], "example": "wallet", "description": "wallet - statistics is counted as before, round - GGR is calculated when roundEnded = true using totalBet/totalWin"}, "disableCloseRoundForBonusMode": {"type": "boolean", "description": "Disables transfer outs for online players", "example": false}, "numberOfRoundsWithoutBets": {"type": "number", "description": "When player does not wager within X round system should block his chat privileges", "example": 10}, "numberOfRoundsWithoutBalance": {"type": "number", "description": "When player lost all the balance and his balance < 1 EUR after Y rounds his chat privileges are disabled.", "example": 10}, "useCountriesFromJurisdiction": {"type": "boolean", "description": "Indicates whether we should use allowed/restricted countries or default country from Jurisdiction for validations", "example": false}, "supportHistoryUrl": {"type": "boolean", "description": "True if an history url will be added to a response in Player API ('/info')", "example": true}, "flatReportsEnabled": {"type": "boolean", "description": "Indicates that flat reports are enabled", "example": true}, "defaultStatusForNewEntity": {"type": "string", "enum": ["normal", "suspended", "maintenance", "blocked_by_admin", "test"], "description": "Default entity status for creating a new entity", "example": "normal"}, "useGameProviderLimits": {"type": "boolean", "description": "Indicates that limits will send as empty object (external limits will be used)", "example": true}, "gameProviderSiteCodes": {"type": "object", "description": "Mapping of merchant's code to game provider known codes", "example": {"pp": "CODE-01"}}, "hashLobbyAndCashierEnabled": {"type": "boolean", "description": "Indicates that hashes of lobby and cashier will be added to startGameToken", "example": true}, "merchantGameRestrictionsUseIpCountries": {"type": "array", "description": "List of country codes for merchant game restriction", "items": {"type": "string", "description": "Country code", "example": "RU"}}, "restrictedIpCountries": {"$ref": "#/definitions/RestrictedIpCountries"}, "newExternalBetWinHistoryEnabled": {"type": "boolean", "description": "Indicates that external bet win history works via Redis-Postgres", "example": true}, "gameLimitsSettings": {"type": "object", "additionalProperties": {"type": "object", "properties": {"toEURMultiplier": {"type": "number", "description": "Currency multiplier to EUR", "example": 500}, "copyLimitsFrom": {"type": "string", "description": "Currency from which limits can be copied", "example": "CLP"}}}, "example": {"USD": {"toEURMultiplier": 2000}, "CNY": {"toEURMultiplier": 3000}}}, "currencyFormatSettings": {"type": "object", "additionalProperties": {"type": "object", "properties": {"showCurrency": {"type": "boolean", "description": "Whether it's needed or not to show currency sign", "example": true}, "decimalPartSeparatorCharacter": {"type": "string", "description": "Decimal part separator character (example: 1.00)", "example": "."}, "decimalPartDigitsCount": {"type": "number", "description": "Decimal part default digits count (example: 10.00)", "example": 2}, "realPartSeparatorCharacter": {"type": "string", "description": "Real part separator character (example: 100,000.00)", "example": ","}, "realPartSeparationDigitsCount": {"type": "number", "description": "Split number real part with comma by defined digits count, processed from right to left (example: 1,000,000)", "example": 2}, "appendCurrencyToLeft": {"type": "boolean", "description": "Append currency symbol to left or right", "example": true}, "shortMode": {"type": "boolean", "description": "Enables short mode postfixes K:10^3, M:10^6, B:10^9 (example $1.45K or $9.99M)", "example": true}, "tickupDecimalsAsLineBet": {"type": "boolean", "description": "Use decimals type for tickups the same as we use for the current line bet value", "example": true}, "decimalPartAppendType": {"type": "number", "description": "Sets displaying type of decimal part (CUT_ZERO_ONLY, CUT_DECIMAL, APPEND_DECIMAL, CUT_INSIGNIFICANT_ZERO)", "enum": [0, 1, 2, 3], "example": 0}}}, "example": {"USD": {"toEURMultiplier": 2000}, "CNY": {"toEURMultiplier": 3000}}}, "social": {"type": "boolean", "description": "Is a social entity", "example": true}, "isFunModeNotSupported": {"type": "boolean", "description": "true if game can only be played in real money mode", "example": true}, "smResultEnabled": {"type": "boolean", "description": "Enable smResult for this entity", "example": true}, "wrapperLauncherVersion": {"type": "string", "description": "Version of the wrapper launcher (for external games)", "example": "1.0.0"}, "tableauBaseUrl": {"type": "string", "description": "Tableau Base Url", "example": "https://bi-as.mn103007.com/#/site/public-asia"}, "tableauTrustServerUrl": {"type": "string", "description": "Tableau Trust Server Url", "example": "https://bi-as.mn103007.com/trusted"}, "biVersion": {"type": "string", "enum": ["local", "global", "both"], "description": "BI version", "example": "local"}, "forcePendingRewards": {"type": "boolean", "description": "If set to true, promo rewards are always added as pending and will be given on game launch", "example": false}, "cacheMergedEntitySettings": {"type": "boolean", "description": "Enables caching merged entity settings", "example": false}, "useRemoteWallet": {"type": "boolean", "description": "Flag to make the entity use the remote wallet", "example": false}, "cacheEntityGames": {"type": "boolean", "description": "Enables caching entity games", "example": false}, "skipPlayerInfoRequest": {"type": "boolean", "description": "Flag that allows to skip requests to PlayerInfo DB table on getting game url", "example": true}, "skipAvailableSiteRequest": {"type": "boolean", "description": "Flag that allows to skip requests to AvailableSite DB table on game start", "example": true}, "skipBlockedPlayerValidation": {"type": "boolean", "description": "Flag that allows to skip blocked player validation on game start", "example": true}, "skipTestPlayerValidation": {"type": "boolean", "description": "Flag that allows to skip test player validation on game start", "example": true}}}, "RegulatoryLink": {"type": "object", "properties": {"urlType": {"type": "string", "description": "Type of url", "example": "link_legal_18"}, "url": {"type": "string", "description": "Url", "example": "http://legal18.com"}}}, "SummaryUserInfo": {"type": "object", "properties": {"username": {"type": "string", "description": "username", "example": "User01"}, "status": {"type": "string", "description": "user status (normal/suspended/locked_by_auth)", "enum": ["normal", "suspended"], "example": "normal"}}}, "UserCreateOptions": {"type": "object", "properties": {"passwordForceChangePeriod": {"type": "integer", "description": "Number of days password expires in, used if isPasswordForceChangeEnabled set to true", "example": 90}}}, "EntityAdditionalInfoRecord": {"type": "object", "example": {"adminEmail": "<EMAIL>"}}, "GameServerSettings": {"type": "object", "properties": {"name": {"type": "string", "description": "GS name", "example": "gs1"}, "description": {"type": "string", "description": "Description", "example": "Some description"}, "roundIdRange": {"type": "array", "items": {"type": "string"}, "example": ["0", "100000"]}, "sessionIdRange": {"type": "array", "items": {"type": "string"}, "example": ["0", "100000"]}}}, "PatchUserData": {"type": "object", "properties": {"username": {"type": "string", "description": "username", "example": "User01"}, "status": {"type": "string", "description": "user status (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "firstName": {"type": "string", "description": "username", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "username", "example": "Snow"}, "phone": {"type": "string", "description": "user phone number", "example": "+42567812357"}, "userType": {"type": "string", "description": "Type of the user (bo, operator_api, studio_user)", "enum": ["bo", "operator_api", "studio_user"], "example": "bo"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/SummaryRoleSchema"}}, "forcePasswordChangePeriodType": {"type": "string", "description": "Type of the period (minutely, hourly, daily, weekly, monthly, yearly)", "enum": ["minutely", "hourly", "daily", "weekly", "monthly", "yearly"], "example": "monthly"}, "forcePasswordChangePeriod": {"type": "number", "example": 5}, "customData": {"type": "object"}}}, "DetailedUserInfo": {"type": "object", "properties": {"username": {"type": "string", "description": "username", "example": "User01"}, "status": {"type": "string", "description": "user status (normal/suspended/locked_by_auth)", "enum": ["normal", "suspended"], "example": "normal"}, "id": {"type": "string", "description": "public id of user", "example": "94RGGRen"}, "firstName": {"type": "string", "description": "username", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "username", "example": "Snow"}, "email": {"type": "string", "description": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "user phone number", "example": "+42567812357"}, "lastLogin": {"type": "string", "description": "The last time a user logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt": {"type": "string", "description": "The time when a user is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time a user updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "entity": {"type": "string", "description": "relative entity path", "example": "TLE:"}, "userType": {"type": "string", "description": "Type of the user (bo, operator_api, studio_user)", "enum": ["bo", "operator_api", "studio_user"], "example": "bo"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/SummaryRoleSchema"}}, "grantedPermissions": {"$ref": "#/definitions/Permissions"}, "forcePasswordChangePeriodType": {"type": "string", "description": "Type of the period (minutely, hourly, daily, weekly, monthly, yearly)", "enum": ["minutely", "hourly", "daily", "weekly", "monthly", "yearly"], "example": "monthly"}, "forcePasswordChangePeriod": {"type": "number", "example": 5}, "customData": {"type": "object"}}}, "DetailedUserInfoWithBlockingInfo": {"type": "object", "properties": {"username": {"type": "string", "description": "username", "example": "User01"}, "status": {"type": "string", "description": "user status (normal/suspended/locked_by_auth)", "enum": ["normal", "suspended"], "example": "normal"}, "id": {"type": "string", "description": "public id of user", "example": "94RGGRen"}, "firstName": {"type": "string", "description": "username", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "username", "example": "Snow"}, "email": {"type": "string", "description": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "user phone number", "example": "+42567812357"}, "lastLogin": {"type": "string", "description": "The last time a user logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt": {"type": "string", "description": "The time when a user is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time a user updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "entity": {"type": "string", "description": "relative entity path", "example": "TLE:"}, "userType": {"type": "string", "description": "Type of the user (bo, operator_api, studio_user)", "enum": ["bo", "operator_api", "studio_user"], "example": "bo"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/SummaryRoleSchema"}}, "blocking": {"type": "object", "properties": {"loginTillDate": {"type": "string", "example": "2018-08-15T14:53:24.353Z"}, "changePasswordTillDate": {"type": "string", "example": "null"}}}, "grantedPermissions": {"$ref": "#/definitions/Permissions"}, "customData": {"type": "object"}}}, "UserPermissionsInfo": {"type": "object", "properties": {"grantedPermissions": {"$ref": "#/definitions/Permissions"}}}, "ChangeEmailData": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "description": "New user email", "example": "<EMAIL>"}}}, "ChangePasswordData": {"type": "object", "required": ["password", "newPassword"], "properties": {"password": {"type": "string", "format": "password", "description": "current password", "example": "123456qaB"}, "newPassword": {"type": "string", "format": "password", "example": "123456testB"}}}, "ForceResetPasswordData": {"type": "object", "required": ["newPassword"], "properties": {"newPassword": {"type": "string", "format": "password", "description": "123456testB"}}}, "AuditSchema": {"type": "object", "description": "Info about audit log", "required": ["auditId", "entityId", "ts", "history", "initiatorType", "initiatorName", "ip", "userAgent", "auditsSummaryId", "auditsSessionId", "auditsSummary"], "properties": {"auditId": {"type": "integer", "description": "id of audit", "example": 12321312}, "entityId": {"type": "integer", "description": "id of owner <PERSON><PERSON><PERSON>", "example": 12321312}, "entity": {"type": "string", "description": "relative entity path", "example": "TLE:"}, "ts": {"type": "string", "description": "The time of audit (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "history": {"type": "string", "description": "Data of message", "example": {}}, "initiatorType": {"type": "string", "description": "Type of initiator", "example": "user", "enum": ["user", "player", "system"]}, "initiatorName": {"type": "string", "description": "Initiator login(code)", "example": "admin"}, "initiatorServiceName": {"type": "string", "description": "Initiator service name", "example": "Operator"}, "initiatorIssueId": {"type": "string", "description": "Initiator <PERSON><PERSON> issue", "example": ""}, "ip": {"type": "string", "description": "Initiator ip adress", "example": "*********"}, "userAgent": {"type": "string", "description": "Initiator userAgent", "example": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_0) AppleWebKit/537.36 (KHTML, like Gecko)"}, "auditsSummaryId": {"type": "string", "description": "id of an auditSummary", "example": "NlqP5RZj"}, "auditsSessionId": {"type": "string", "description": "id of an auditSession", "example": "e4316f11-3cb4-49a9-98f8-662ece1f698c"}, "auditsSummary": {"$ref": "#/definitions/AuditSummarySchema"}}}, "AuditSessionSchema": {"type": "object", "description": "Info about audited session", "required": ["id", "entityId", "initiatorName", "startedAt", "finishedAt"], "properties": {"id": {"type": "string", "description": "id of audit session", "example": "92a2da56-4883-415d-8fb5-5a52dd2eca5d"}, "entityId": {"type": "string", "description": "id of Entity", "example": "W4RkGRen"}, "initiatorName": {"type": "string", "description": "name of session owner", "example": "admin"}, "startedAt": {"type": "string", "description": "The time of session start (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "finishedAt": {"type": "string", "description": "The time of session finish (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}}}, "AuditSummarySchema": {"type": "object", "description": "Info about audit summary", "required": ["id"], "properties": {"id": {"type": "string", "description": "id of audit summary", "example": "NlqP5RZj"}, "eventName": {"type": "string", "description": "Name of the event within something happened", "example": "Lobby"}, "summary": {"type": "string", "description": "Event description", "example": "Creates lobby"}, "path": {"type": "string", "description": "url that provide audited event", "example": "/lobbies"}, "method": {"type": "string", "description": "api method of url", "example": "post", "enum": ["post", "put", "get", "delete", "patch", "cron"]}}}, "AgentSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of agent", "example": "58dad665"}, "domain": {"type": "string", "description": "Domain or IP address of friedly site", "example": "***********"}, "affiliateCode": {"type": "string", "description": "code for domain", "example": "game-siberia-server"}, "status": {"type": "string", "description": "suspended or normal (by default)", "example": "suspended", "enum": ["normal", "suspended"]}, "title": {"type": "string", "description": "title for this agent", "example": "Title for this site"}, "description": {"type": "string", "description": "comment for this site", "example": "Just plain text about site or brand"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}, "brandTitle": {"type": "string", "description": "title of brand", "example": "<PERSON><PERSON> Lett Win"}}}, "AvailableSiteSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of Available Site", "example": "6H73u9Gi"}, "entityId": {"type": "string", "description": "public id of Entity (Brand or Merchant)", "example": "iPr0Si78R"}, "title": {"type": "string", "description": "Title or description of Available Site", "example": "Site Central and East regions"}, "isDefault": {"type": "boolean", "description": "this will indicate if this is the default site for this operator", "example": false}, "operatorSiteGroupName": {"type": "string", "description": "this column is textual and will be used to group several sites for reporting", "example": "bwin.com"}, "externalCode": {"type": "string", "description": "This will be used for external code identification of a site", "example": "sub.bwin.com"}, "url": {"type": "string", "description": "Available Site URL", "example": "super-pot.8bingo8.com"}, "status": {"type": "string", "description": "suspended or normal (by default)", "example": "suspended", "enum": ["normal", "suspended"]}, "insertedAt": {"type": "string", "description": "The time when info is posted (ISO 8601 timestamp)", "example": "2018-02-21T11:21:15.764Z"}}}, "AvailableSiteCreateSchema": {"type": "object", "properties": {"title": {"type": "string", "description": "Title or description of Available Site", "example": "Site Central and East regions"}, "url": {"type": "string", "description": "Available Site URL", "example": "bingo.saia.com"}, "status": {"type": "string", "description": "suspended or normal (by default)", "example": "normal", "enum": ["normal", "suspended"]}, "isDefault": {"type": "boolean", "description": "this will indicate if this is the default site for this operator", "example": false}, "operatorSiteGroupName": {"type": "string", "description": "this column is textual and will be used to group several sites for reporting", "example": "bwin.com"}, "externalCode": {"type": "string", "description": "This will be used for external code identification of a site", "example": "sub.bwin.com"}}}, "AvailableSiteBulkOperation": {"type": "array", "items": {"type": "object", "properties": {"sites": {"type": "array", "items": {"type": "string"}}, "action": {"type": "string", "description": "Action for bulk operation", "example": "activate", "enum": ["activate", "deactivate", "remove"]}}}}, "SummaryRoleSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Public id of Role", "example": "W4RBGR9n"}, "title": {"type": "string", "description": "Role's title", "example": "admin"}, "description": {"type": "string", "description": "Role's description", "example": "Role for managing users"}, "owned": {"type": "boolean", "description": "Is it owned entity's role or parent's shared role. If owned:true user can edit this Role.", "example": true}}}, "ExtendedRoleSchema": {"allOf": [{"$ref": "#/definitions/SummaryRoleSchema"}, {"type": "object", "properties": {"isShared": {"type": "boolean", "description": "Is this role available for child entity", "example": true}}}]}, "DetailedRoleSchema": {"allOf": [{"$ref": "#/definitions/ExtendedRoleSchema"}, {"type": "object", "properties": {"permissions": {"$ref": "#/definitions/Permissions"}}}]}, "AddItemRoleData": {"type": "object", "required": ["title", "permissions", "isShared"], "properties": {"title": {"type": "string", "description": "Role's title", "example": "admin"}, "description": {"type": "string", "description": "Role's description", "example": "Role for managing users"}, "isShared": {"type": "boolean", "description": "Is this role available for child entity", "example": true}, "permissions": {"$ref": "#/definitions/Permissions"}}}, "UpdateItemRoleData": {"type": "object", "required": ["title", "permissions", "isShared"], "properties": {"title": {"type": "string", "description": "Role's title", "example": "admin"}, "description": {"type": "string", "description": "Role's description", "example": "Role for managing users"}, "isShared": {"type": "boolean", "description": "Is this role available for child entity", "example": true}, "permissions": {"$ref": "#/definitions/Permissions"}, "pathTo": {"type": "string", "description": "Business entity path for role relocation", "example": "path"}}}, "AddItemAgentData": {"type": "object", "properties": {"domain": {"type": "string", "description": "Domain or IP address of friedly site", "example": "***********"}, "status": {"type": "string", "description": "suspended or normal (by default)", "example": "suspended", "enum": ["normal", "suspended"]}, "affiliateCode": {"type": "string", "description": "affiliate code", "example": "brother<PERSON>ite"}, "title": {"type": "string", "description": "title for this agent", "example": "Title for this site"}, "description": {"type": "string", "description": "comment for this site", "example": "Just plain text about site or brand"}}}, "OAuthTokenInfo": {"type": "object", "allOf": [{"$ref": "#/definitions/LoginInfo"}, {"type": "object", "properties": {"expiresAt": {"type": "string", "description": "Expiration date of the access token", "example": "2024-12-19T08:42:29.924Z"}, "refreshTokenExpiresAt": {"type": "string", "description": "Expiration date of the refresh token", "example": "2024-12-26T08:42:29.924Z"}}}]}, "LoginInfo": {"type": "object", "properties": {"key": {"type": "string", "description": "entity key", "example": "4e85fb5c-cc34-472b-ac4a-69bbcf1b73c0"}, "username": {"type": "string", "description": "username", "example": "USER1"}, "lastPasswordUpdate": {"type": "string", "description": "Last date when password was updated", "example": "2018-12-19T08:42:29.924Z"}, "accessToken": {"type": "string", "format": "byte", "description": "access token", "example": "eyJ9.ecmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn0.fR_EZZ_YD2u4SXjjcHi66VkFUC9w7uXP-FpYLA-wS-_ApTQ"}, "grantedPermissions": {"type": "string", "description": "jwt encoded permissions. Request headers should contain initiatorServiceName=Backoffice to have grantedPermissions parameter in response", "example": "eyJ9.ecmQzgyMiwiaXNzIjoic2t5d2luZGdyb3VwIn0.fR_EZZ_YD2u4SXjjcHi66VkFUC9w7uXP-FpYLA-wS-_ApTQ"}}}, "ChallengeSentInfo": {"type": "object", "required": ["authType"], "properties": {"authType": {"type": "string", "description": "Selected auth type", "example": "email"}, "contactInfo": {"type": "object", "properties": {"email": {"type": "string", "description": "Destination where auth code as sent", "example": "sk*****@gmail.com"}}}, "totpUri": {"type": "string", "description": "One-time password uri for google auth qr code", "example": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANQAAADUCAYAAADk3g0YAAAAAklEQVR4AewaftIAAAqDSURBVO3BQY7"}, "gaSecretKey": {"type": "string", "description": "Secret key for google auth manual setup", "example": "dj2g os2v oiqh jf2r tll6 wiu4 yypv ylhh"}}}, "UserAuthTypesInfo": {"type": "object", "properties": {"defaultAuthType": {"type": "string", "description": "Default auth type", "example": "email"}, "userAuthTypes": {"type": "array", "description": "User auth types", "items": {"type": "string"}, "example": ["sms", "email", "google"]}}}, "LoginPlayerInfo": {"type": "object", "required": ["code", "token"], "properties": {"code": {"type": "string", "description": "code of player", "example": "Pl0039SrvInd04VIP02"}, "token": {"type": "string", "format": "byte", "description": "access token", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}, "isPasswordTemp": {"type": "boolean", "description": "indicate whether password is temporary. If no could be omitted", "example": true}}}, "LoginPlayerDataInfo": {"type": "object", "required": ["code", "password"], "properties": {"code": {"type": "string", "description": "player's code", "example": "PL0034SrvCN02"}, "password": {"type": "string", "format": "password", "description": "password is longer than or equal 8 letters, is no longer than or equal 255 and contains at least one letter, one uppercase letter and one digit", "minLength": 8, "maxLength": 255, "example": "19Letters&4Numbers&3Signs!"}, "force": {"type": "boolean", "description": "<PERSON>p another active session", "example": true}}}, "LogoutTerminalPlayerDataInfo": {"type": "object", "required": ["code", "terminalId"], "properties": {"code": {"type": "string", "description": "player's code", "example": "PL0034SrvCN02"}, "terminalId": {"type": "string", "description": "player's terminal public id", "example": "6p0hySHe"}}}, "OperationDescriptions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "POST /entities"}, "description": {"type": "string", "example": "Create a new entity under a specific parent"}, "permissions": {"$ref": "#/definitions/Permissions"}}}, "example": [{"id": "POST /entities", "description": "Create a new entity under a specific parent", "permissions": ["entity", "entity:create"]}, {"id": "GET /entities/{path}", "description": "Get specific entity information, no child entities and including all balances", "permissions": ["entity", "entity:view"]}, {"id": "PUT /entities/{path}/suspended", "description": "change the status of the entity to suspended. change operationsMap of sub entities will fail", "permissions": ["entity", "entity:change-state"]}, {"id": "DELETE /entities/{path}/suspended", "description": "change the status of the entity to normal", "permissions": ["entity", "entity:change-state"]}]}, "GameGroupCreateData": {"type": "object", "required": ["name", "description"], "properties": {"name": {"type": "string", "description": "game group name", "example": "VIP-1"}, "description": {"type": "string", "description": "game group description", "example": "VIP Level 1"}}}, "GameGroupFilter": {"type": "object", "required": ["games", "currencies"], "properties": {"id": {"type": "number", "description": "Filter identificator", "example": "publicId"}, "maxTotalBet": {"type": "number", "description": "Max total bet (in EUR)", "example": 250}, "minTotalBet": {"type": "number", "description": "Min total bet (in EUR)", "example": 1}, "defTotalBet": {"type": "number", "description": "Default total bet (in EUR)", "example": 20}, "winCapping": {"type": "number", "description": "Total win cap (in EUR)", "example": 1000}, "maxExposure": {"type": "number", "description": "Max exposure is maxTotalBet * highestPrize (in EUR)", "example": 250}, "games": {"type": "array", "items": {"type": "string"}, "description": "List of game codes (empty list for all games)", "example": ["sw_rm"]}, "currencies": {"type": "array", "items": {"type": "string"}, "example": ["USD", "EUR"], "description": "List of currencies (empty list for all currencies)"}, "createdAt": {"type": "string", "description": "The time when a filter is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time when a filter is updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "groupId": {"type": "string", "description": "Public group id (View)", "example": "publicId"}, "ignoreInvalid": {"type": "boolean", "description": "Ignore invalid filtering (if stakeAll is empty after filtering). By default raise error.", "example": "true"}}}, "GameGroupInfo": {"allOf": [{"$ref": "#/definitions/GameGroupCreateData"}, {"type": "object", "properties": {"id": {"type": "string", "description": "public id of game group", "example": "7hJHT4RN"}, "isOwner": {"type": "boolean", "description": "indicates if game group belongs to key entity", "example": true}, "isDefault": {"type": "boolean", "description": "Player will be created with this group", "example": false}, "filters": {"type": "array", "items": {"$ref": "#/definitions/GameGroupFilter"}}}}]}, "GameGroupLimitsInfo": {"type": "object", "properties": {"limits": {"$ref": "#/definitions/GameGroupLimitsByCurrencyCode"}, "overrideDefault": {"type": "boolean"}, "game": {"$ref": "#/definitions/GameInfoForGameGroupLimits"}, "gamegroup": {"$ref": "#/definitions/GameGroupInfo"}}}, "GameInfoForGameGroupLimits": {"type": "object", "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"type": "object", "description": "game info by locale", "additionalProperties": {"$ref": "#/definitions/GameDescription"}}, "providerCode": {"type": "string", "description": "Provider code", "example": "PR"}, "providerTitle": {"type": "string", "description": "Provider title", "example": "Provider 1"}, "status": {"type": "string", "description": "suspended or normal (by default)", "example": "normal", "enum": ["normal", "suspended"]}, "settings": {"type": "object", "description": "game settings - any key-value pairs"}, "releaseDate": {"type": "string", "description": "date when game was added into system", "example": "2018-08-22T12:28:51.382Z"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}, "features": {"$ref": "#/definitions/GameFeatures"}}}, "GameDescription": {"type": "object", "description": "game info", "required": ["name", "description"], "properties": {"name": {"type": "string", "description": "game name", "example": "Slot Name"}, "description": {"type": "string", "description": "game description", "example": "Slot description"}}}, "GameSettings": {"type": "object", "properties": {"jackpotId": {"type": "object", "description": "mapping of jackpot types to jackpot instances assigned to the game", "example": {"sw-jpgame": "JP-ID1"}}, "rtpConfigurator": {"type": "object", "description": "RTP configuration", "example": {"rtp": 95, "rtpDeduction": 1}}, "newLimitsDisabled": {"type": "boolean", "description": "disable new game limits system for specific entity game", "example": true}, "marketing": {"type": "object", "description": "configuration of contributions to marketing jackpots", "example": {"contributions": [{"jackpotId": "MRKT-JP", "contribution": 0.5}]}}, "roundExpireAt": {"type": "number", "description": "time when round context should be expired (in minutes) for this game (overrides entity setting of roundExpireAt)", "example": 120}, "startGameTokenExpiresIn": {"type": "number", "description": "startGameToken duration time (in seconds) for the specific game (overrides config startGameToken.expiresIn value)", "example": 7200}, "wushiEnable": {"type": "boolean", "description": "if true wushiJackpot can be contributed, otherwise - no", "example": true}, "enableWushiSelectionScreen": {"type": "boolean", "description": "Enables the Wushi selection screen. Operator should be asked if he wants to show the selection screen before a player starts the game.", "example": true}, "allowOppositeBets": {"type": "boolean", "description": "to disable opposite betting", "example": true}, "tableFullCoverage": {"type": "boolean", "description": "to disable opposite betting for Roulette", "example": true}, "decreaseMaxBetSupported": {"type": "boolean", "description": "true if custom game group limit filters is allowed to decrease max bet. This option will override game features.", "example": false}, "increaseMinBetSupported": {"type": "boolean", "description": "true if custom game group limit filters is allowed to increase min bet. This option will override game features.", "example": false}, "finalizationSupport": {"$ref": "#/definitions/BrandFinalizationType"}, "limitFeaturesToMaxTotalStake": {"$ref": "#/definitions/LimitFeaturesToMaxTotalStake"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "mustWinJackpotBundled": {"$ref": "#/definitions/MustWinJackpotBundled"}, "buyFeatureJrsdGrLegacy": {"type": "boolean", "description": "false if need set buyFeature = false to jrsdSettings for Greece", "example": false}, "buyFeatureJrsdPtLegacy": {"type": "boolean", "description": "true if need set buyFeature = true to jrsdSettings for Portugal", "example": false}}}, "EntityGameSettings": {"allOf": [{"$ref": "#/definitions/GameSettings"}, {"type": "object", "properties": {"countries": {"$ref": "#/definitions/CountriesRestrictions"}}}]}, "GameHistoryRenderType": {"type": "number", "description": "History render type - 0 - old render, 1 - new render,", "example": 0, "default": 0}, "GameCodeInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}}}, "GameShortInfo": {"type": "object", "required": ["code"], "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "status": {"type": "string", "description": "status of item (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "settings": {"$ref": "#/definitions/EntityGameSettings", "description": "game settings - any key-value pairs"}, "limitFilters": {"$ref": "#/definitions/LimitFiltersByCurrencyCode", "description": "Limit filters"}, "urlParams": {"$ref": "#/definitions/EntityGameUrlParams", "description": "Entity game url params"}, "hidden": {"type": "boolean", "description": "hidden games cannot be started, but can be finished", "example": false}}}, "EntityGameStructure": {"type": "object", "properties": {"name": {"type": "string", "description": "Entity name", "example": "Entity1"}, "key": {"type": "string", "description": "Entity key", "example": "a22b1e39-e6c9-4033-963e-392fc2069492"}, "type": {"type": "string", "description": "Entity type", "example": "entity"}, "games": {"type": "array", "items": {"$ref": "#/definitions/GameShortInfo"}}, "child": {"type": "array", "items": {"$ref": "#/definitions/EntityGameStructure"}}}}, "BulkAddEntityGamesResult": {"type": "object", "properties": {"addedGamesCount": {"type": "number", "description": "number of added games", "example": "99"}}}, "GameInfo": {"type": "object", "required": ["code", "defaultInfo", "info"], "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"type": "object", "description": "game info by locale", "additionalProperties": {"$ref": "#/definitions/GameDescription"}}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}, "labels": {"type": "array", "items": {"$ref": "#/definitions/LabelInfo"}}, "providerCode": {"type": "string", "description": "Provider code", "example": "PR"}, "providerTitle": {"type": "string", "description": "Provider title", "example": "Provider 1"}, "status": {"type": "string", "description": "suspended or normal (by default)", "example": "normal", "enum": ["normal", "suspended", "test", "hidden"]}, "settings": {"$ref": "#/definitions/EntityGameSettings", "description": "game settings - any key-value pairs"}, "releaseDate": {"type": "string", "description": "date when game was added into system", "example": "2018-08-22T12:28:51.382Z"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}, "limitFilters": {"$ref": "#/definitions/LimitFiltersByCurrencyCode", "description": "Limit filters"}, "urlParams": {"$ref": "#/definitions/EntityGameUrlParams", "description": "Entity game url params"}, "providerGameCode": {"type": "string", "description": "Game code", "example": "GAME001"}, "limitFiltersWillBeApplied": {"type": "boolean", "description": "Read-only field which means that game group limit filters will be applied to the entity game if exists (totalBetMultiplier, game slot, decreaseMaxBetSupported or increaseMinBetSupported from game or entityGame)", "example": true}, "externalGameId": {"type": "string", "description": "External game id received from the operator", "example": "1234"}, "domain": {"type": "string", "description": "Static domain", "example": "static.gameprovider.com"}}}, "GamesTerminalInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "providerTitle": {"type": "string", "description": "provider title", "example": "test"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}, "features": {"$ref": "#/definitions/GameFeatures"}, "live": {"$ref": "#/definitions/GameLive"}}}, "GameBriefInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"type": "object", "description": "game info by locale", "additionalProperties": {"$ref": "#/definitions/GameDescription"}}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}, "labels": {"type": "array", "items": {"$ref": "#/definitions/LabelInfo"}}, "providerCode": {"type": "string", "description": "Provider code", "example": "PR"}, "providerTitle": {"type": "string", "description": "Provider title", "example": "Provider 1"}, "features": {"$ref": "#/definitions/GameFeatures"}, "releaseDate": {"type": "string", "description": "date when game was added into system", "example": "2018-08-22T12:28:51.382Z"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "providerGameCode": {"type": "string", "example": "sw_provider"}, "url": {"type": "string", "description": "Game URL", "example": "http://api.test/game"}, "status": {"type": "string", "description": "game status", "example": "normal"}, "rtpInfo": {"$ref": "#/definitions/RTPInfo"}, "limitFiltersWillBeApplied": {"type": "boolean", "description": "Read-only field which means that game group limit filters will be applied to the entity game if exists (totalBetMultiplier, game slot, decreaseMaxBetSupported or increaseMinBetSupported from game or entityGame)", "example": true}, "jackpots": {"$ref": "#/definitions/GameJackpots"}, "live": {"$ref": "#/definitions/GameLive"}}}, "LabelGroupInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of labelGroup", "example": "7hJHT4RN"}, "group": {"type": "string", "description": "LabelGroup name", "example": "AM"}, "type": {"type": "string", "description": "LabelGroup type: entity or game", "example": "entity"}, "relationType": {"type": "string", "description": "LabelGroup realtionType: o/m", "example": "o"}}}, "LabelInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of label", "example": "7hJHT4RN"}, "title": {"type": "string", "description": "Label name", "example": "<PERSON><PERSON>"}, "group": {"$ref": "#/definitions/LabelGroupInfo"}}}, "CreateLabelInfo": {"type": "object", "required": ["title", "groupId"], "properties": {"title": {"type": "string", "description": "Label name", "example": "<PERSON><PERSON>"}, "groupId": {"type": "string", "description": "Label group ID", "example": "pQ3513OE"}}}, "UpdateLabelInfo": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "description": "Label name", "example": "<PERSON><PERSON>"}}}, "DeleteLabelInfo": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string", "description": "Label public id", "example": "pQ3513OE"}}}, "GameCategoryInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "Game category public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Game category name for EN", "example": "Some category"}, "description": {"type": "string", "description": "Game category description for EN", "example": "Some category description"}, "status": {"type": "string", "description": "Game category status. normal | suspended", "example": "normal"}, "isEntityOwner": {"type": "boolean", "description": "Indicates is entity owner of category", "example": true}, "items": {"type": "array", "description": "Game Category filter of games - gameCodes, labels, providers, intersections", "items": {"$ref": "#/definitions/GameCategoryItem"}}, "icon": {"type": "string", "description": "Game category icon for EN", "example": "text"}, "translations": {"type": "object", "description": "translations for title, description and icon for all languages except for EN", "properties": {"eu": {"$ref": "#/definitions/GameCategoriesTranslationItem"}}}}}, "GameCategoryShortInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "Game category public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Game category name for EN", "example": "Some category"}, "type": {"type": "string", "description": "Type of game category", "enum": ["general", "gamestore"]}, "description": {"type": "string", "description": "Game category description for EN", "example": "Some category description"}, "status": {"type": "string", "description": "Game category status. normal | suspended", "example": "normal"}, "items": {"type": "array", "description": "Game Category filter of games - gameCodes, labels, providers, intersections", "items": {"$ref": "#/definitions/GameCategoryItem"}}, "icon": {"type": "string", "description": "Game category icon for EN", "example": "text"}, "translations": {"type": "object", "description": "translations for title, description and icon for all languages except for EN", "properties": {"eu": {"$ref": "#/definitions/GameCategoriesTranslationItem"}}}}}, "GameCategoryCreateData": {"type": "object", "required": ["title", "type"], "properties": {"title": {"type": "string", "description": "Game category name for EN", "example": "Some category"}, "type": {"type": "string", "description": "Type of game category", "enum": ["general", "gamestore"]}, "description": {"type": "string", "description": "Game category description for EN", "example": "Some category description"}, "status": {"type": "string", "description": "Game category status. normal | suspended", "example": "normal"}, "items": {"type": "array", "description": "Game Category filter of games - gameCodes, labels, providers, intersections", "items": {"$ref": "#/definitions/GameCategoryItem"}}, "icon": {"type": "string", "description": "Game category icon for EN", "example": "text"}, "translations": {"type": "object", "description": "translations for title, description and icon for all languages except for EN", "properties": {"eu": {"$ref": "#/definitions/GameCategoriesTranslationItem"}}}}}, "GameCategoryUpdateData": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "description": "Game category name for EN", "example": "Some category"}, "description": {"type": "string", "description": "Game category description for EN", "example": "Some category description"}, "status": {"type": "string", "description": "Game category status. normal | suspended", "example": "normal"}, "items": {"type": "array", "description": "Game Category filter of games - gameCodes, labels, providers, intersections", "items": {"$ref": "#/definitions/GameCategoryItem"}}, "icon": {"type": "string", "description": "Game category icon for EN", "example": "text"}, "translations": {"type": "object", "description": "translations for title, description and icon for all languages except for EN", "properties": {"eu": {"$ref": "#/definitions/GameCategoriesTranslationItem"}}}}}, "GameCategoryUpdateOrderingData": {"type": "object", "required": ["newPosition"], "properties": {"newPosition": {"type": "number", "description": "New position of game category, starts from 0", "example": 0}}}, "GameCategoryItem": {"type": "object", "required": ["type"], "properties": {"id": {"type": "string", "description": "Object publicId or code", "example": "sw_fufish"}, "type": {"type": "string", "description": "Type of object - game, provider, label, intersection", "example": "game"}, "items": {"type": "array", "description": "This attribute use with intersection type", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "W4RkGRen"}, "type": {"type": "string", "example": "label"}}}}}}, "TerminalData": {"type": "object", "properties": {"id": {"type": "string", "description": "Terminal public id", "example": "vOq32eRb"}, "title": {"type": "string", "description": "Terminal title", "example": "Golendon Indie Club No003 Terminal 2018-04-21-No0010056"}, "status": {"type": "string", "description": "status of terminal (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "brandId": {"type": "string", "description": "Brand public id", "example": "gT78JJg9"}, "lobbyId": {"type": "string", "description": "Lobby public id", "example": "Qlw12kI8"}, "createdAt": {"type": "string", "description": "The time when a terminal is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time when a terminal is updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "player": {"type": "object", "description": "Information about active player if it exists"}}}, "TerminalCreateData": {"type": "object", "properties": {"title": {"type": "string", "description": "Terminal title", "example": "Golendon Indie Club No003 Terminal 2018-04-21-No0010056"}, "status": {"type": "string", "description": "status of terminal (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}}}, "TerminalTokenData": {"type": "object", "properties": {"terminalToken": {"type": "string", "description": "token for managing terminals", "example": "ejlo023.acab1fr="}}}, "TerminalTokenCreateData": {"type": "object", "properties": {"playerUrl": {"type": "string", "description": "Url of player api. Optional. System uses default value for all terminals.", "example": "https://api.game-zome-game.com:8017/v1/"}, "terminalUrl": {"type": "string", "description": "Url of terminal api. Optional. System uses default value for all terminals.", "example": "https://api.game-zome-terminals.com:8009/v1/"}}}, "DownloadableLobbyData": {"allOf": [{"$ref": "#/definitions/LobbyShortData"}, {"type": "object", "properties": {"path": {"type": "string", "description": "Business entity path", "example": "east-coast:ferry-casino:titanic-type:"}}}]}, "DownloadableLobbyExtendedData": {"allOf": [{"$ref": "#/definitions/DownloadableLobbyData"}, {"type": "object", "properties": {"terminalToken": {"type": "string", "description": "token for working with MAPI under lobby", "example": "efiis823du2u==smdj.ajsdhj333d"}, "theme": {"type": "object", "description": "Custom information about theme of lobby", "example": {}}}}]}, "LobbyShortData": {"type": "object", "properties": {"id": {"type": "string", "description": "Lobby public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Lobby title", "example": "Some lobby title"}, "description": {"type": "string", "description": "Lobby description", "example": "Some lobby description"}, "status": {"type": "string", "description": "status of lobby (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "info": {"type": "object", "description": "Any meta information of lobby", "example": {"thumb": "http://meme.info/nooooo.png"}}, "createdAt": {"type": "string", "description": "time when a Lobby was created (ISO 8601 timestamp)", "example": "2018-09-19T12:30:49.083Z"}, "updatedAt": {"type": "string", "description": "last time a Lobby was updated (ISO 8601 timestamp)", "example": "2018-09-19T12:33:06.909Z"}}}, "LobbyData": {"allOf": [{"$ref": "#/definitions/LobbyShortData"}, {"type": "object", "properties": {"brandId": {"type": "string", "example": "s0GBK8gK", "description": "Brand id"}, "theme": {"type": "object", "description": "Custom information about theme of lobby", "example": {}}, "isDefault": {"type": "boolean", "description": "Default lobby which will be used in launch game", "example": false}}}]}, "UpdateLobbyData": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "description": "Lobby title", "example": "Some lobby title"}, "description": {"type": "string", "description": "Lobby description", "example": "Some lobby description"}, "info": {"type": "object", "description": "Any meta information of lobby", "example": {"thumb": "http://meme.info/nooooo.png"}}, "theme": {"type": "object", "description": "Custom information about theme of lobby", "example": {}}, "isDefault": {"type": "boolean", "description": "Default lobby which will be used in launch game", "example": false}}}, "PromotionShortInfo": {"type": "object", "required": ["title", "type", "startDate", "endDate", "intervalType"], "properties": {"id": {"type": "string", "description": "Promotion public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Promotione title", "example": "Christmas promo"}, "type": {"type": "string", "description": "Promotion type.", "example": "freebet"}, "status": {"type": "string", "description": "Promo status, active or inactive", "example": "inactive"}, "state": {"type": "string", "description": "Promo state", "example": "pending"}, "brandId": {"type": "string", "description": "Brand public id this promotion belongs to", "example": "bRaNd0o1"}, "brandPath": {"type": "string", "description": "Path to brand this promotion belongs to", "example": "ENT1:BRAND1"}, "startDate": {"type": "string", "description": "Promo start date in UTC format", "example": "2019-12-23T15:00:00.000Z"}, "endDate": {"type": "string", "description": "Promo end date in UTC format", "example": "2019-12-24T16:00:00.000Z"}, "timezone": {"type": "string", "description": "timezone of promo", "example": "America/Los_Angeles"}, "description": {"type": "string", "description": "Promo description", "example": "Some description of this promo"}, "intervalType": {"type": "string", "description": "Promo calculation period. Hourly, daily, weekly or monthly", "example": "weekly"}, "daysOfWeek": {"type": "array", "description": "Days of week to run calculation", "items": {"type": "string"}, "example": ["FRI", "SAT"]}, "daysOfMonth": {"type": "array", "description": "Days of month to run calculation", "items": {"type": "number"}, "example": ["1", "2", "3", "29", "30"]}, "timeOfDay": {"type": "string", "description": "Time of day to run calculation", "example": "12:30:00"}, "createdUserId": {"type": "string", "description": "Public id of a user who created this promo", "example": "aZQ900OE"}, "createdUserName": {"type": "string", "description": "Public name of a user who created this promo", "example": "USER1"}, "createdDate": {"type": "string", "description": "Promo creation date", "example": "2018-12-23T16:00:00.000Z"}, "everStarted": {"type": "boolean", "description": "True if this promo has ever been running", "example": true}, "externalId": {"type": "string", "description": "Optional reference to promo created in operator's system (operator does not have to have one)", "example": "extPromoId"}}}, "PromotionCreateInfo": {"type": "object", "required": ["title", "type", "endDate", "intervalType"], "properties": {"title": {"type": "string", "description": "Promotion title", "example": "Christmas promo"}, "status": {"type": "string", "description": "Promotion status: active or inactive", "example": "active"}, "type": {"type": "string", "description": "Promotion type", "example": "freebet"}, "conditions": {"$ref": "#/definitions/PromoCondition"}, "startDate": {"type": "string", "description": "Promo start date in UTC format", "example": "2019-12-23T15:00:00.000Z"}, "startRewardOnGameOpen": {"type": "boolean", "description": "Indicates if to start reward on start of game", "example": false}, "timezone": {"type": "string", "description": "Timezone of promo", "example": "America/Los_Angeles"}, "endDate": {"type": "string", "description": "Promo end date in UTC format", "example": "2019-12-24T16:00:00.000Z"}, "description": {"type": "string", "description": "Promo description", "example": "Some description of this promo"}, "intervalType": {"type": "string", "description": "Promo calculation period. Hourly, daily, weekly or monthly", "example": "weekly"}, "daysOfWeek": {"type": "array", "description": "Days of week to run calculation", "items": {"type": "string"}, "example": ["FRI", "SAT"]}, "daysOfMonth": {"type": "array", "description": "Days of month to run calculation", "items": {"type": "number"}, "example": ["1", "2", "3", "29", "30"]}, "timeOfDay": {"type": "string", "description": "Time of day to run calculation", "example": "12:30:00"}, "customerIds": {"type": "array", "description": "Player codes to assign promotion to", "items": {"type": "string"}, "example": ["p_code1", "p_code2"]}, "externalId": {"type": "string", "description": "Optional reference to promo created in operator's system (operator does not have to have one)", "example": "extPromoId"}, "providerSpecificOptions": {"type": "object", "description": "Optional object which contains provider specific information (applies to external game provider promotions)", "properties": {"type": {"type": "string", "description": "The external game provider bonus type", "example": "infinity"}, "duration": {"type": "number", "description": "The bonus duration in seconds. Required for PatePlay infinity free bet bonuses only", "example": 30}, "ttl": {"type": "number", "description": "Bonus time to live (after it has been started by the player) in seconds", "example": 3600}}}}}, "CreateSimpleFreebetPromoData": {"type": "object", "description": "Freebet data that does not belong to promo that should be applied to player", "required": ["title", "startDate", "endDate"], "properties": {"title": {"type": "string", "description": "Promo title", "example": "Quick freebet promo"}, "startDate": {"type": "string", "description": "promo start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "example": "2018-12-10T15:00:00.000Z"}, "endDate": {"type": "string", "description": "promo start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "example": "2018-12-10T16:00:00.000Z"}, "reward": {"$ref": "#/definitions/SimploPromoFreebetRewardInfo"}}}, "SimploPromoFreebetRewardInfo": {"type": "object", "properties": {"freebetAmount": {"type": "number", "description": "Number of freebets within a promo", "example": 15}, "games": {"type": "array", "items": {"$ref": "#/definitions/FreebetGameInfo"}}}}, "FreebetGameInfo": {"type": "object", "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "sw_fazer6"}, "coins": {"type": "array", "description": "Coins config for game", "items": {"type": "object"}, "example": [{"USD": {"coin": 0.01}}, {"CNY": {"coin": 0.1}}]}}}, "PromotionWithoutIds": {"allOf": [{"$ref": "#/definitions/PromotionCreateInfo"}, {"type": "object", "properties": {"rewards": {"$ref": "#/definitions/PromoRewardArray"}}}]}, "Promotion": {"allOf": [{"$ref": "#/definitions/PromotionCreateInfo"}, {"type": "object", "properties": {"id": {"type": "string", "description": "Promotion public id", "example": "pQ3513OE"}, "status": {"type": "string", "description": "Promotion status: active or inactive", "example": "active"}, "createdUserId": {"type": "string", "description": "Public id of a user who created this promo", "example": "aZQ900OE"}, "createdUserName": {"type": "string", "description": "Public name of a user who created this promo", "example": "USER1"}, "updatedUserId": {"type": "string", "description": "Public id of a user who updated this promo", "example": "aZQ900OE"}, "createdAt": {"type": "string", "description": "Promo creation date", "example": "2019-12-23T15:00:00.000Z"}, "updatedAt": {"type": "string", "description": "Promo update date", "example": "2019-12-23T16:00:00.000Z"}, "everStarted": {"type": "boolean", "description": "True if this promo has ever been running", "example": true}, "active": {"type": "boolean", "description": "True if promo is active", "example": true}, "archived": {"type": "boolean", "description": "True if promo is archived"}, "brandId": {"type": "string", "example": "s0GBK8gK", "description": "Brand id"}, "owner": {"type": "string", "example": "operator"}, "rewards": {"$ref": "#/definitions/PromoRewardArray"}}}]}, "PromotionUpdateData": {"allOf": [{"$ref": "#/definitions/PromotionCreateInfo"}, {"type": "object", "properties": {"id": {"type": "string", "description": "Promotion public id", "example": "pQ3513OE"}, "status": {"type": "string", "description": "Promo status, active or inactive", "example": "inactive"}, "rewards": {"$ref": "#/definitions/PromoRewardArray"}}}]}, "PromoCondition": {"type": "object", "properties": {"and": {"type": "array", "items": {"$ref": "#/definitions/PromoCondition"}}, "or": {"type": "array", "items": {"$ref": "#/definitions/PromoCondition"}}, "value": {"type": "number", "description": "Condition value", "example": 99}, "operator": {"type": "string", "description": "operator to apply to value(<, <=, =, >, >=)", "example": "<="}, "valueField": {"type": "string", "description": "Type of value(deposit_amount or deposit_count)", "example": "deposit_amount"}}}, "PromoRewardArray": {"type": "array", "items": {"$ref": "#/definitions/PlayerFreebetReward"}, "example": [{"freebetAmount": 10, "expirationPeriod": 5, "expirationPeriodType": "daily", "games": [{"gameCode": "sw_gsxr", "coins": [{"USD": {"coin": 0.01}}, {"CNY": {"coin": 0.1}}]}]}]}, "PromotionPlayer": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "Player code", "example": "playerCode1"}, "promoId": {"type": "string", "description": "Encoded PromoId", "example": "qw1aqI"}, "status": {"type": "string", "description": "Player promotion status", "enum": ["pending", "confirmed", "started", "revoked"]}, "expireAt": {"type": "string", "description": "Timestamp when promotion rewards expire for player", "example": "2019-12-23T15:00:00.000Z"}}}, "PlayerPromotion": {"type": "object", "properties": {"promoId": {"type": "string", "description": "Promotion id", "example": "W4RkGRen"}, "status": {"type": "string", "description": "Player promotion status", "enum": ["pending", "started", "revoked", "failed"]}, "expireAt": {"type": "string", "description": "Timestamp when promotion rewards expire for player", "example": "2019-12-23T15:00:00.000Z"}, "playerCode": {"type": "string", "description": "Player code", "example": "PLAYER001"}}}, "PlayerPromotionWithFreeBetLeft": {"allOf": [{"$ref": "#/definitions/PlayerPromotion"}, {"type": "object", "properties": {"freeBetLeft": {"type": "number", "description": "The number of free bets left for the player to consume", "example": 2}}}]}, "PlayerFreebetReward": {"type": "object", "properties": {"currency": {"type": "string", "description": "Currency code", "example": "USD"}, "amount": {"type": "number", "description": "Left count of free bets", "example": 5}, "games": {"type": "array", "description": "List of game codes where free bets can be played", "items": {"type": "object", "properties": {"gameCode": {"type": "string", "description": "Code of game"}, "coins": {"type": "array", "items": {"type": "object"}}}}, "example": {"gameCode": "sw_fr", "coins": [{"USD": {"coin": 0.01}}, {"CNY": {"coin": 0.1}}]}}, "startDate": {"type": "string", "description": "Timestamp when player can start using free bets", "example": "2019-12-23T15:00:00.000Z"}, "endDate": {"type": "string", "description": "Timestamp when free bets will expire", "example": "2019-12-24T16:00:00.000Z"}, "timezone": {"type": "string", "description": "timezone of promo", "example": "America/Los_Angeles"}}}, "CreatePlayerBonusDto": {"type": "object", "required": ["type", "title", "description", "amount", "gameCode", "currency", "coin", "expiresAt"], "properties": {"type": {"type": "string", "description": "Type of the player bonus", "enum": ["freebet"]}, "title": {"type": "string", "description": "Title of the bonus", "example": "Welcome Bonus"}, "description": {"type": "string", "description": "Description of the bonus", "example": "Bonus awarded on first deposit"}, "amount": {"type": "integer", "description": "Bonus amount", "example": 100}, "gameCode": {"description": "The game code(s) for which the bonus will be applied. Either array of game codes or single game code string.", "example": "sw_bb"}, "currency": {"type": "string", "description": "The currency for which the bonus will be applied", "example": "EUR"}, "coin": {"type": "number", "description": "The coin value of the bonus", "example": 0.01}, "expiresAt": {"type": "string", "description": "Expiration date of the bonus in ISO 8601 format", "example": "2025-12-31T23:59:59Z"}, "externalId": {"type": "string", "description": "Optional external system identifier", "example": "c1425a9f-e7a5-4df2-8f7c-69e3ec3d04c7"}}}, "PlayerBonus": {"allOf": [{"$ref": "#/definitions/CreatePlayerBonusDto"}, {"type": "object", "required": ["id"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique bonus identifier", "example": "8f3b7ac6-4d29-4cd3-bc92-7623c63fdbe4"}, "createdAt": {"type": "string", "format": "date-time", "description": "Promo creation date", "example": "2019-12-23T15:00:00.000Z"}}}]}, "PlayerFreebetPromotion": {"allOf": [{"$ref": "#/definitions/PlayerPromotion"}, {"type": "object", "properties": {"promoId": {"type": "string", "description": "Promotion id", "example": "W4RkGRen"}, "freebets": {"type": "array", "items": {"$ref": "#/definitions/PlayerFreebetReward"}}}}]}, "PlayerBonusCoinReward": {"type": "object", "properties": {"amount": {"type": "number", "description": "Left count of bonus coins", "example": 5}, "games": {"type": "array", "description": "List of game codes where bonus coins can be played", "items": {"type": "string", "example": "sw_omqjp"}}, "startDate": {"type": "string", "description": "Timestamp when player can start using bonus coins", "example": "2019-12-23T15:00:00.000Z"}, "endDate": {"type": "string", "description": "Timestamp when bonus coins will expire", "example": "2019-12-24T16:00:00.000Z"}}}, "PlayerBonusCoinPromotion": {"allOf": [{"$ref": "#/definitions/PlayerPromotion"}, {"type": "object", "properties": {"bonusCoins": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonusCoinReward"}}}}]}, "PlayersPromotionAddStatuses": {"type": "object", "properties": {"playerCode1": {"type": "string", "description": "Player code with adding status", "example": "OK"}, "playerCode2": {"type": "string", "description": "Player code with adding status", "example": "Promotion already added"}}}, "BonusCoinProlongData": {"type": "object", "properties": {"amount": {"type": "number", "description": "Count of bonus coins to add to the rewards", "example": 10}, "expirationPeriod": {"type": "number", "description": "Time interval that reward is prolonged", "example": 1}, "expirationPeriodType": {"type": "string", "description": "Time interval type. hourly(hours), daily(days), weekly(weeks) or monthly(months)", "example": "daily"}, "providerSpecificOptions": {"type": "object", "description": "Optional object which contains provider specific information (applies to external game provider promotions)", "properties": {"type": {"type": "string", "description": "The external game provider bonus type", "example": "infinity"}, "duration": {"type": "number", "description": "The bonus duration in seconds. Required for PatePlay infinity free bet bonuses only", "example": 30}, "ttl": {"type": "number", "description": "Bonus time to live (after it has been started by the player) in seconds", "example": 3600}}}}}, "PromoProjectionAndActualInfo": {"type": "object", "properties": {"projected": {"type": "object", "properties": {"reach": {"type": "number", "description": "Projected reach value", "example": 1500}, "participants": {"type": "number", "description": "Projected participants value", "example": 2500}, "payout": {"type": "number", "description": "Projected payout value", "example": 10000}}}, "actual": {"type": "object", "properties": {"reach": {"type": "number", "description": "Actual reach value", "example": 1700}, "participants": {"type": "number", "description": "Actual participants value", "example": 3000}, "payout": {"type": "number", "description": "Actual payout value", "example": 12000}}}}}, "PlayerJpContribution": {"type": "object", "properties": {"dateHour": {"type": "string", "description": "Aggregated period", "example": "2018-01-03T17:00:00.000Z"}, "brandId": {"type": "string", "description": "brand id", "example": "hzO8Gsb"}, "playerCode": {"type": "string", "description": "Player code", "example": "PL10032MOD01T2GROUP03"}, "gameCode": {"type": "string", "description": "Game code", "example": "pt-allad-reel-jp"}, "currency": {"type": "string", "description": "Currency code", "example": "CNY"}, "jackpotId": {"type": "string", "description": "Jackpot id", "example": "OMQ-JP-TWO"}, "pool": {"type": "string", "description": "Jackpot poolname in jackpotId context", "example": "Middle-amount"}, "seedAmount": {"type": "number", "description": "Amount, contributed to the seed part of the JP (player's currency)", "example": 12.5}, "progressiveAmount": {"type": "number", "description": "Amount, contributed to the progressive part of the JP (player's currency)", "example": 6.5}, "totalBetAmount": {"type": "number", "description": "Full bet amount of game stakes used for the Jackpot replenishment (player's currency)", "example": 6.5}, "jpWinAmount": {"type": "number", "description": "Jackpot winning amount (player's currency)", "example": 15146.6}, "jpCurrency": {"type": "string", "description": "JP currency code", "example": "EUR"}, "seedAmountJpCurrency": {"type": "number", "description": "Amount, contributed to the seed part of the JP (JP currency)", "example": 12.5}, "progressiveAmountJpCurrency": {"type": "number", "description": "Amount, contributed to the progressive part of the JP (JP currency)", "example": 6.5}, "totalBetAmountJpCurrency": {"type": "number", "description": "Full bet amount of game stakes used for the Jackpot replenishment (JP currency)", "example": 6.5}, "jpWinAmountJpCurrency": {"type": "number", "description": "Jackpot winning amount (JP currency)", "example": 15146.6}, "totalBetCount": {"type": "integer", "description": "Total count of bets used for the Jackpot replenishment", "example": 120}, "jpWinCount": {"type": "integer", "description": "Total count of Jackpot wins", "example": 2}, "firstActivity": {"type": "string", "description": "First activity (contribution or win) within aggregated period", "example": "2018-01-03T17:10:01.020Z"}, "lastActivity": {"type": "string", "description": "Last activity (contribution or win) within aggregated period", "example": "2018-01-03T17:10:29.854Z"}, "seedWin": {"type": "number", "description": "Amount, contributed to the seed part of the JP", "example": -560}, "progressiveWin": {"type": "number", "description": "Amount, contributed to the progressive part of the JP", "example": 10000}}}, "JpContribution": {"type": "object", "properties": {"dateHour": {"type": "string", "description": "Aggregated period", "example": "2018-01-03T17:00:00.000Z"}, "brandId": {"type": "string", "description": "Brand id", "example": "jk2j3ncD8"}, "gameCode": {"type": "string", "description": "Game code", "example": "pt-allad-reel-jp"}, "currency": {"type": "string", "description": "JP currency code", "example": "USD"}, "seedAmount": {"type": "number", "description": "Amount, contributed to the seed part of the JP (JP currency)", "example": 12.5}, "progressiveAmount": {"type": "number", "description": "Amount, contributed to the progressive part of the JP (JP currency)", "example": 6.5}, "totalBetAmount": {"type": "number", "description": "Full bet amount of game stakes used for the Jackpot replenishment (JP currency)", "example": 6.5}, "jpWinAmount": {"type": "number", "description": "Jackpot winning amount", "example": 15146.6}, "totalBetCount": {"type": "integer", "description": "Total count of bets used for the Jackpot replenishment", "example": 120}, "jpWinCount": {"type": "integer", "description": "Total count of Jackpot wins", "example": 2}, "firstActivity": {"type": "string", "description": "First activity (contribution or win) within aggregated period", "example": "2018-01-03T17:10:01.020Z"}, "lastActivity": {"type": "string", "description": "Last activity (contribution or win) within aggregated period", "example": "2018-01-03T17:10:29.854Z"}, "seedWin": {"type": "number", "description": "Amount, contributed to the seed part of the JP", "example": -560}, "progressiveWin": {"type": "number", "description": "Amount, contributed to the progressive part of the JP", "example": 10000}}}, "Jurisdiction": {"type": "object", "properties": {"title": {"type": "string", "description": "Jurisdiction title", "example": "Juris<PERSON>"}, "code": {"type": "string", "description": "Jurisdiction code", "example": "GIB"}, "description": {"type": "string", "description": "GIB Jurisdiction description", "example": "Some description"}, "defaultCountry": {"type": "string", "description": "Default country", "example": "Some description"}, "allowedCountries": {"type": "array", "items": {"type": "string"}, "description": "List of allowed countries", "example": ["RU", "BY"]}, "restrictedCountries": {"type": "array", "items": {"type": "string"}, "description": "List of restricted countries", "example": ["RU", "BY"]}, "allowedJackpotConfigurationLevel": {"type": "integer", "description": "Allowed jackpot configuration level according to jurisdiction requirements", "enum": [0, 1, 2, 3, 4, 5, 6, 99], "example": 99}, "settings": {"type": "object", "description": "Settings of jurisdiction", "example": {"showRTP": true, "rulesDateStamped": true, "autoPlayUntilFeatureEnabled": true, "autoPlayLimitEnabled": true, "autoPlayLossLimitEnabled": true, "autoPlayLossLimitDefaultValue": 500, "autoPlaySingleWinLimitEnabled": true, "autoPlaySingleWinLimitDefaultValue": 1000, "stopAutoPlayOnJP": true, "showClockOnMobile": true, "fastPlay": true, "turboPlus": true, "turbo": true, "jpWinPushNotificationEnabled": false, "saveTurboModeState": false, "gamble": false, "dynamicMaxTotalBetLimitEnabled": false, "dynamicMaxTotalBetLimit": {"defaultMaxTotalBet": 5, "defaultTotalBet": 2, "currency": "EUR"}}}}}, "JurisdictionCreateInfo": {"allOf": [{"$ref": "#/definitions/Jurisdiction"}, {"type": "object"}], "required": ["code"]}, "JurisdictionUpdateInfo": {"properties": {"id": {"type": "string", "description": "Jurisdiction public id", "example": "pQ3513OE"}}, "allOf": [{"$ref": "#/definitions/Jurisdiction"}, {"type": "object"}]}, "JurisdictionInfo": {"allOf": [{"$ref": "#/definitions/Jurisdiction"}, {"type": "object", "properties": {"id": {"type": "string", "description": "Jurisdiction public id", "example": "pQ3513OE"}, "createdUser": {"type": "string", "description": "Public id of a user who created this jurisdiction", "example": "aZQ900OE"}, "updatedUser": {"type": "string", "description": "Public id of a user who updated this jurisdiction", "example": "aZQ900OE"}, "createdDate": {"type": "string", "description": "Jurisdiction creation date", "example": "2017-12-23T16:00:00.000Z"}, "updatedDate": {"type": "string", "description": "Jurisdiction update date", "example": "2017-12-23T16:00:00.000Z"}}}]}, "PlayerGameURLInfo": {"type": "object", "required": ["url", "token"], "properties": {"url": {"type": "string", "description": "game URL for specific player", "example": "http://super_game.com/"}, "token": {"type": "string", "format": "byte", "description": "player token to access game", "example": "oJqXX2tkAADKn3MpcM9kVbVk53neuIYI62dEkYdYubl+9lyXRECjQww3VsmEPfMoUkO6uqB56WDPhPGdS3aGnQ"}}}, "LimitsByCurrencyCode": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Limits"}, "example": {"USD": {"maxTotalStake": 2000, "stakeAll": [1, 2, 3, 5], "stakeDef": 1, "stakeMax": 5, "stakeMin": 1, "winMax": 200}, "CNY": {"maxTotalStake": 3000, "stakeAll": [2, 3, 5, 10], "stakeDef": 2, "stakeMax": 10, "stakeMin": 2, "winMax": 400}}}, "GameGroupLimitsByCurrencyCode": {"type": "object", "additionalProperties": {"$ref": "#/definitions/GameGroupLimits"}, "example": {"USD": {"maxTotalStake": 2000, "stakeAll": [1, 2, 3, 5], "stakeDef": 1, "stakeMax": 100, "stakeMin": 1}, "CNY": {"maxTotalStake": 3000, "stakeAll": [2, 3, 5, 10], "stakeDef": 2, "stakeMax": 200, "stakeMin": 2}}}, "Limits": {"type": "object", "required": ["maxTotalStake", "stakeAll", "stakeDef", "stakeMax", "stakeMin", "winMax"], "properties": {"maxTotalStake": {"type": "number", "description": "max total stake", "example": 1000}, "stakeAll": {"type": "array", "description": "all possible stake", "items": {"type": "number"}, "example": [0.1, 0.5, 1, 2, 3, 5]}, "stakeDef": {"type": "number", "description": "default stake", "example": 1}, "stakeMax": {"type": "number", "description": "max stake", "example": 5}, "stakeMin": {"type": "number", "description": "min stake", "example": 0.1}, "winMax": {"type": "number", "description": "max win", "example": 2000}}}, "GameGroupLimits": {"type": "object", "properties": {"maxTotalStake": {"type": "number", "description": "the maximum total stake which is available in the game", "example": 1000}, "stakeAll": {"type": "array", "description": "an array of all coin bets which are possible per line", "items": {"type": "number"}, "example": [0.1, 0.5, 1, 2, 3, 5]}, "stakeDef": {"type": "number", "description": "the default stake which is used at the first game launch ", "example": 1}, "stakeMax": {"type": "number", "description": "the maximum value from the stakeAll array", "example": 5}, "stakeMin": {"type": "number", "description": "the minimum value from the stakeAll array", "example": 0.1}}}, "GameProviderInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "game provider public id", "example": "jJr70Mq1"}, "code": {"type": "string", "description": "game provider code", "example": "provider1"}, "title": {"type": "string", "description": "game provider title", "example": "Provider 1"}, "status": {"type": "string", "description": "game provider status (normal/suspended)", "enum": ["normal", "suspended"]}, "isTest": {"type": "boolean", "description": "is game provider test", "enum": [true, false]}, "mustStoreExtHistory": {"type": "string", "description": "Set to true for external game providers that need game history to be saved on our side", "example": "false"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}}}, "Balance": {"type": "object", "required": ["main"], "properties": {"main": {"type": "number", "description": "balance", "example": 1020.36}}}, "Balances": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Balance"}, "example": [{"USD": 1020.36}, {"CNY": 3600.23}]}, "GlobalSettings": {"type": "object", "required": ["userIdleTimeout", "userLoginAttempts"], "properties": {"userIdleTimeout": {"type": "integer", "description": "user idle timeout", "example": 60}, "userLoginAttempts": {"type": "integer", "description": "number of user login attempts", "example": 5}}}, "Brand": {"type": "object", "properties": {"isTest": {"type": "boolean", "description": "indicates whether merchant is used for testing only", "example": false}}}, "Merchant": {"type": "object", "required": ["type", "code"], "properties": {"type": {"type": "string", "description": "merchant type", "example": "ipm"}, "code": {"type": "string", "description": "merchant identifier", "example": 14}, "params": {"$ref": "#/definitions/MerchantParams"}, "isTest": {"type": "boolean", "description": "indicates whether merchant is used for testing only", "example": false}, "proxyId": {"type": "string", "description": "Proxy public ID", "example": "feE3Sb39"}, "lastTestsPassing": {"type": "string", "description": "Date of last integration tests passing", "example": "2018-12-10T11:12:34.927Z"}, "proxy": {"$ref": "#/definitions/Proxy"}}}, "UpdateMerchantData": {"type": "object", "properties": {"params": {"$ref": "#/definitions/MerchantParams"}, "type": {"type": "string", "description": "Merchant type"}, "code": {"type": "string", "description": "Merchant code"}, "proxyId": {"type": "string", "description": "Proxy public ID", "example": "feE3Sb39"}}}, "MerchantParams": {"type": "object", "description": "merchant additional parameters", "required": ["password", "serverUrl"], "properties": {"isPasswordBonusAuth": {"type": "boolean", "description": "auth type in bonus payment. if true - use merch  password, if false - use hash.", "example": false}, "password": {"type": "string", "format": "password", "example": "pwd"}, "serverUrl": {"type": "string", "example": "http://localhost:8000"}, "supportTransfer": {"type": "boolean", "description": "indicates whether merchant supports coins in/out functionality", "example": true}, "walletPerGame": {"type": "boolean", "description": "indicates whether merchant arcade games support wallet per game (not one internal wallet for all action games). It works only if merchant has supportTransfer == true", "example": false}, "supportPlayMoney": {"type": "boolean", "description": "indicates whether merchant supports playMoney functionality", "example": false}, "keepAliveSec": {"type": "number", "description": "timer in seconds to keep client session alive (used with coins in/out functionality)", "example": 30}, "isPromoInternal": {"type": "boolean", "description": "indicates if system should manage promotions for merchant in internal wallet", "example": false}, "supportBothInternalExternalPromo": {"type": "boolean", "description": "Some operators can support both promo stored on sw/egp side and promo stored on their operator. Also, some EGP enforce operators to store promo on their side, so some of our clients can choose to store skywind games free bet balance on their side and EGP games free bet balance on EGP side. The purpose of this flag is to allow operators to create promo on EGP side using our API even if isPromoInternal: false if supportBothInternalExternalPromo: true", "example": false}, "supportForceFinishAndRevert": {"type": "boolean", "description": "allows force finish and revert round for merchant", "example": false}, "refreshSessionForNewGame": {"type": "boolean", "description": "Allow to refresh session for new game", "example": false}, "forceFinishAndRevertInSWWalletOnly": {"type": "boolean", "description": "set true if operator closes rounds in his system prior to calling ours to settle round - in this case, we will not call round settlement at operator's side", "example": false}, "gsId": {"type": "number", "description": "Game server id", "example": 90}, "gpId": {"type": "string", "description": "Game provider id", "example": "skywind"}, "retryPolicy": {"type": "object", "properties": {"delay": {"type": "number", "description": "Delay with which request will be retried (in ms)", "example": 1000}, "maxAttempts": {"type": "number", "description": "Max attempts of request retrying (in ms)", "example": 50}, "cancelMaxAttempts": {"type": "number", "description": "<PERSON> attempts to cancel request (in ms)", "example": 50000}}}, "certSettings": {"type": "object", "properties": {"useCert": {"type": "boolean", "description": "Flag which shows should ssl protocol options be appended into request to merchant server", "example": true}, "settings": {"type": "object", "properties": {"cert": {"type": "string", "example": "ALICE CERTIFICATE"}, "key": {"type": "string", "example": "RSA KEY"}, "password": {"type": "string", "example": "qwertyu"}, "ca": {"type": "string", "example": "ca"}}}}}, "sameUrlForTerminalLoginAndTicket": {"type": "boolean", "description": "set true to login terminal player at the same endpoint as on ticket validation", "example": false}, "gamble": {"type": "boolean", "description": "true will correspond to ON Gamble feature", "example": false}, "gameLogoutOptions": {"$ref": "#/definitions/GameLogoutOptions"}, "regulatorySettings": {"type": "object", "description": "regulation settings (such like AAMS for Italy)", "example": {"merchantRegulation": "romanian", "notificationTitle": "Notice", "operatorName": "italianCasino777", "operatorLicenseId": "itLicense345432"}}, "gameClientCurrencyReplacement": {"type": "object", "description": "instruction for game client to replace currency code that is displayed to player", "example": {"INR": "PS", "XAF": "TS"}}, "verifyPlayerOnGameStart": {"type": "boolean", "description": "flag for some operators to enforce player verification on game start and not during get game url phase", "example": false}, "reportJPContributionOnDebitForSeamless": {"type": "boolean", "description": "if true, it will add jp contribution amount to debit requests of jp games (applicable to seamless integration)", "example": false}, "reportJPWinStatisticOnCreditForSeamless": {"type": "boolean", "description": "if true, it will add jp win statistic to credit requests of jp games (applicable to seamless integration)", "example": false}, "excludeJPContributionOnDebit": {"type": "boolean", "description": "if true, it will exclude jp contribution amount for debit requests of jp games", "example": false}, "excludeJPWinStatisticOnCredit": {"type": "boolean", "description": "if true, it will exclude jp win statistic for credit requests of jp games", "example": false}, "enforceSingleTransferInAndOutPerRound": {"type": "boolean", "description": "if true, game-server should enforce a 'single transfer per round' rule", "example": false}, "sendNumericTrxId": {"type": "boolean", "description": "if true, wallet adapter must send trx_id as a numeric value instead of hardcoded string", "example": false}, "refundBetInsteadOfRetry": {"type": "boolean", "description": "if true (should be true for all new merchants), wallet adapter will: 1) not retry failed bet 2) trigger Refund bet process upon getting unknown/timeout error on bet", "example": false}, "dontSendZeroPayments": {"description": "Set true if operator does not want to receive 0-bets and 0-wins for non-transfer payments, such bets are sent by SW by default on freespins, for example. Note, that if round starts with 0-bet (in bonus round for example) or if its technical 0-transfer - such 'opening' 0-bet still will be sent. 'Closing' 0-wins will still be sent at the end of the round.", "type": "boolean", "example": false}, "supportPromoDistributionType": {"type": "boolean", "description": "If true will add 'distributionType' to deferred payment request", "example": false}, "bonusApiAdditionalFields": {"type": "object", "properties": {"supportOperationTs": {"type": "boolean", "description": "Mean that 'operation_ts' should be send with deferred payment request to operator", "example": false}}}, "gameRestrictionsUseIp": {"type": "boolean", "description": "Use player ip in game launch country restrictions", "example": false}}}, "GameLogoutOptions": {"type": "object", "description": "Additional parameters to manage player session on game server side", "properties": {"type": {"type": "string", "format": "Determines in which cases send logout, all - for all cases, unfinished - if only round ins't finished", "enum": ["all", "unfinished"], "example": "unfinished"}, "maxRetryAttempts": {"type": "number", "description": "Max retry attempts for logout", "example": 12}, "maxSessionTimeout": {"type": "number", "description": "Interval to autodetect that player is not active(minutes)", "example": 5}}}, "CurrencyReport": {"type": "array", "items": {"type": "object", "required": ["currency", "playedGames"], "properties": {"currency": {"type": "string", "description": "Currency code", "example": "USD"}, "playedGames": {"type": "integer", "description": "Number of games played", "example": 112503}, "bets": {"type": "number", "description": "Sum of all bets", "example": 256432.12}, "winnings": {"type": "number", "description": "Sum of all winnings", "example": 548123.34}, "ggr": {"type": "number", "description": "Gross Gambling Revenue (bets-winnings)", "example": 804555.46}, "betsUsd": {"type": "number", "description": "Sum of all bets in USD", "example": 256432.12}, "winningsUsd": {"type": "number", "description": "Sum of all winnings in USD", "example": 548123.34}, "ggrUsd": {"type": "number", "description": "Gross Gambling Revenue in USD", "example": 804555.46}}}}, "PlayersReport": {"type": "array", "items": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "Player code", "example": "PL0001"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "playedGames": {"type": "number", "description": "Count of played games", "example": 27182}, "totalBets": {"type": "number", "description": "Sum of the bets", "example": 1216851546}, "totalWins": {"type": "number", "description": "<PERSON><PERSON> of the wins", "example": 2851546}, "totalJpWins": {"type": "number", "description": "<PERSON><PERSON> of the jackpot wins", "example": 15000}, "totalFreebetWins": {"type": "number", "description": "<PERSON><PERSON> of the freebet wins", "example": 2300}, "GGR": {"type": "number", "description": "Resulting GGR", "example": 1546}, "RTP": {"type": "number", "description": "Resulting RTP", "example": 0.93123741241}, "debits": {"type": "number", "description": "Sum of all debits", "example": 1500}, "credits": {"type": "number", "description": "Sum of all credits", "example": 10000}}}}, "PlayersDailyReport": {"type": "array", "items": {"type": "object", "properties": {"day": {"type": "string", "description": "day", "example": "2017/08/15"}, "gameCode": {"type": "string", "description": "Game code", "example": "Pl00x0304ExtBr07"}, "rounds": {"type": "number", "description": "Count of played rounds", "example": 105865}, "players": {"type": "number", "description": "Count of unique players", "example": 1395}, "bet": {"type": "number", "description": "Sum of the bets", "example": 120096}, "win": {"type": "number", "description": "<PERSON><PERSON> of the wins", "example": 273146}, "revenue": {"type": "number", "description": "Resulting GGR", "example": 1546}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}}}}, "ReportGGR": {"type": "array", "items": {"type": "object", "properties": {"brandId": {"type": "string", "description": "Brand public id", "example": "U98S67D4"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Sum of the bets", "example": 6723543}, "win": {"type": "number", "description": "<PERSON><PERSON> of the wins", "example": 4545677}, "revenue": {"type": "number", "description": "Resulting Gross Gaming Revenue", "example": 2177866}, "jackpotWin": {"type": "number", "description": "Total jackpot wins", "example": 2177866}, "freeBetWin": {"type": "number", "description": "Total free bet wins", "example": 2177866}}}}, "GameHistory": {"type": "object", "properties": {"roundId": {"type": "string", "description": "Round public id", "example": 100000063}, "brandId": {"type": "string", "description": "Brand public id", "example": "feE3Sb39"}, "playerCode": {"type": "string", "description": "Player code", "example": "PLAYER1"}, "gameCode": {"type": "string", "description": "Game code", "example": "sw_mrmnky"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 2}, "revenue": {"type": "number", "description": "Revenue", "example": -1.9}, "firstTs": {"type": "string", "description": "time of first action in round", "example": "2017-07-14T07:07:01.080Z"}, "ts": {"type": "string", "description": "time of last action in round", "example": "2017-07-14T07:07:11.930Z"}, "finished": {"type": "boolean", "description": "Whether the round has ended", "example": true}, "isTest": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "balanceBefore": {"type": "number", "description": "Player's balance before round", "example": 25000}, "balanceAfter": {"type": "number", "description": "Player's balance after round", "example": 24950}, "device": {"type": "string", "description": "Player's device", "example": "web"}, "insertedAt": {"type": "string", "example": "2018-07-04T11:14:03.275Z"}, "totalEvents": {"type": "number", "description": "Count of events", "example": 1}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}, "recoveryType": {"type": "string", "enum": ["revert", "force-finish", "finalize"], "description": "Recovery type for rounds that were resolved manually", "example": "force-finish"}, "extraData": {"type": "object", "properties": {"extRoundId": {"type": "string", "description": "PT round id", "example": "extRound1"}}}}}, "UnfinishedGameHistory": {"allOf": [{"$ref": "#/definitions/GameHistory"}, {"type": "object", "required": ["gameContextId"], "properties": {"gameContextId": {"type": "string", "description": "Id of a game context of unfinished game", "example": "games:context:Ki6hY78r:PL0001:sw_al:web"}, "status": {"type": "string", "enum": ["broken", "unfinished"], "description": "Status of round", "example": "broken"}}}]}, "ForceFinishRoundResult": {"type": "object", "properties": {"result": {"type": "string", "description": "Force finish round result", "enum": ["force-finished"], "example": "force-finished"}}}, "FinalizeRoundResult": {"type": "object", "properties": {"result": {"type": "string", "description": "Finalize round result. 'finalized' - returned in case of finalization was finished successfully, 'finalization-requested' - returned when operation is triggered with 'waitForCompletion: false' flag - it will run in background and the result should be checked later via history", "enum": ["finalized", "finalization-requested"], "example": "finalized"}, "roundStatistics": {"type": "object", "description": "Round statistics after finalize. Returned if waitForCompletion = true", "properties": {"totalBet": {"type": "number", "description": "Total bet", "example": 1}, "totalWin": {"type": "number", "description": "Total win", "example": 11.42}, "totalEvents": {"type": "number", "description": "Count of events", "example": 5}, "balanceBefore": {"type": "number", "description": "Player balance before round", "example": 850.5}, "balanceAfter": {"type": "number", "description": "Player balance after round", "example": 860.2}, "totalJpContribution": {"type": "number", "description": "<PERSON><PERSON> of all jackpot contributions in terms of the round", "example": 0}, "totalJpWin": {"type": "number", "description": "<PERSON><PERSON> of all jackpot wins in terms of the round", "example": 0}, "currentBet": {"type": "number", "example": 1}, "betsCount": {"type": "number", "example": 1}, "startedAt": {"type": "string", "description": "The time of round start (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "broken": {"type": "boolean", "example": true}, "finishedAt": {"type": "string", "description": "The time of round finish (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}}}}}, "RevertRoundResult": {"type": "object", "properties": {"result": {"type": "string", "description": "Revert round result", "enum": ["reverted"], "example": "reverted"}}}, "RetryRoundResult": {"type": "object", "properties": {"result": {"type": "string", "description": "Finish round result", "enum": ["finished"], "example": "finished"}}}, "BonusPlayerStats": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "Player code", "example": "PL00564Vt17"}, "brandId": {"type": "string", "description": "Brand public id", "example": "Ki6hY78r"}, "promoId": {"type": "string", "description": "Promotion id", "example": "8uUii8zA"}, "promo": {"type": "object", "description": "Promotion additional information with title, status and so on", "properties": {"id": {"type": "string", "description": "Promotion public id", "example": "pQ3513OE"}, "createdUserId": {"type": "string", "description": "Public id of a user who created this promo", "example": "aZQ900OE"}, "updatedUserId": {"type": "string", "description": "Public id of a user who updated this promo", "example": "aZQ900OE"}, "createdAt": {"type": "string", "description": "Promo creation date", "example": "2019-12-23T15:00:00.000Z"}, "updatedAt": {"type": "string", "description": "Promo update date", "example": "2019-12-23T16:00:00.000Z"}, "everStarted": {"type": "boolean", "description": "True if this promo has ever been running", "example": true}, "active": {"type": "boolean", "description": "True if promo is active", "example": true}, "archived": {"type": "boolean", "description": "True if promo is archived"}, "brandId": {"type": "string", "example": "s0GBK8gK", "description": "Brand id"}, "title": {"type": "string", "description": "Promotion title", "example": "Christmas promo"}, "type": {"type": "string", "description": "Promotion type", "example": "freebet"}, "conditions": {"$ref": "#/definitions/PromoCondition"}, "startDate": {"type": "string", "description": "Promo start date in UTC format", "example": "2019-12-23T15:00:00.000Z"}, "timezone": {"type": "string", "description": "Timezone of promo", "example": "America/Los_Angeles"}, "endDate": {"type": "string", "description": "Promo end date in UTC format", "example": "2019-12-24T16:00:00.000Z"}, "description": {"type": "string", "description": "Promo description", "example": "Some description of this promo"}, "intervalType": {"type": "string", "description": "Promo calculation period. Hourly, daily, weekly or monthly", "example": "weekly"}, "daysOfWeek": {"type": "array", "description": "Days of week to run calculation", "items": {"type": "string"}, "example": ["FRI", "SAT"]}, "daysOfMonth": {"type": "array", "description": "Days of month to run calculation", "items": {"type": "number"}, "example": ["1", "2", "3", "29", "30"]}, "timeOfDay": {"type": "string", "description": "Time of day to run calculation", "example": "12:30:00"}, "customerIds": {"type": "array", "description": "Player codes to assign promotion to", "items": {"type": "string"}, "example": ["p_code1", "p_code2"]}}}, "dateReceived": {"type": "string", "description": "Date received", "example": "2018-11-16T16:37:13.613Z"}, "startDate": {"type": "string", "description": "Start date", "example": "2018-11-10T16:00:00.000Z"}, "expirationDate": {"type": "string", "description": "Expiration date", "example": "2018-12-28T15:00:00.000Z"}}}, "BonusPromoStats": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "Player code", "example": "Clb09-PL0012-St12"}, "brandId": {"type": "string", "description": "Brand public id", "example": "Ki6hY78r"}, "promoId": {"type": "string", "description": "Promotion id", "example": "8uUii8zA"}, "amountAwarded": {"type": "number", "description": "Amount awarded", "example": "143"}, "amountPlayed": {"type": "number", "description": "Amount played", "example": "97"}, "expirationDate": {"type": "string", "description": "Expiration date", "example": "2018-12-28T15:00:00.000Z"}}}, "BonusExpiredStats": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "Player code", "example": "Clb09-PL0012-St12"}, "brandId": {"type": "string", "description": "Brand public id", "example": "Ki6hY78r"}, "promoId": {"type": "string", "description": "Promotion id", "example": "8uUii8zA"}, "amountExpired": {"type": "number", "description": "Amount expired", "example": "3412"}, "lastLogin": {"type": "string", "description": "Player's last login date", "example": "2018-11-21T14:03:10.121Z"}, "expirationDate": {"type": "string", "description": "Expiration date", "example": "2018-12-28T15:00:00.000Z"}}}, "PromoStats": {"type": "object", "properties": {"participantsTotal": {"type": "string", "description": "Count of players in promo", "example": "1395"}, "reachedTotal": {"type": "string", "description": "Count of reached players in promo", "example": "1395"}, "payoutTotal": {"type": "number", "description": "payout total", "example": "2788000"}, "participantsByDay": {"type": "array", "description": "Deep stat by days", "items": {"type": "object"}, "example": [{}]}}}, "GameHistorySpin": {"type": "object", "required": ["balanceAfter", "balanceBefore", "spinNumber", "type", "currency", "bet", "win", "endOfRound", "isPayment"], "properties": {"spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "type": {"type": "string", "description": "History item type", "example": "slot"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 1.5}, "endOfRound": {"type": "boolean", "description": "Whether this spin was at the end of the round", "example": true}, "ts": {"type": "string", "description": "Time of spin (ISO 8601 timestamp)", "example": "2017-02-16T16:37:13.613Z"}, "test": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "isPayment": {"type": "boolean", "description": "If it's true, spin has transaction"}, "balanceBefore": {"type": "number", "description": "Player balance before spin", "example": 1000}, "balanceAfter": {"type": "number", "description": "Player balance after spin", "example": 998.8}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}, "details": {"$ref": "#/definitions/GameHistoryDetails"}}}, "GameHistoryDetails": {"type": "object", "required": ["roundId", "spinNumber", "gameId", "gameVersion"], "properties": {"roundId": {"type": "string", "description": "Round public id", "example": 200000134}, "spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "gameId": {"type": "string", "description": "Game module id", "example": "sw_gol"}, "gameVersion": {"type": "string", "description": "Game module version", "example": "0.1.1"}, "details": {"type": "string", "description": "Game event details", "example": "{ data: \"Some spin data\" }"}, "initSettings": {"type": "string", "description": "Game init settings", "example": "{ data: \"Some game init information\" }"}, "ts": {"type": "string", "description": "Time of spin (ISO 8601 timestamp)", "example": "2018-06-14T20:56:57.714Z"}, "historyInfo": {"type": "string", "description": "Information about history", "example": "{ url: \"http://gc.some-gamesetver.com/gamehistory/2.9.2/index.html\", historyRenderType: 2 }"}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}}}, "GameHistorySmResult": {"type": "object", "required": ["smResult"], "properties": {"smResult": {"type": "string", "description": "Game events encoded as sm_result", "example": "0:2;3;4;2;3#2;5;5;2;3#0;7;6;4;3#"}}}, "GameHistoryVisualization": {"type": "object", "required": ["imageUrl"], "properties": {"imageUrl": {"type": "string", "description": "Game event details visualisation link", "example": "http://example.com/gamehistory/0.0.1/history.html?data=bhESknjk578.eyJlSWQiJZCI6Njk1NzCwiiZXhwIjoxNTMwNzgzNNJKNCDCjYzLCSSvdXAifQ&url=site.com&language=en"}, "ttl": {"type": "number", "description": "Token is expires in this time (in seconds)", "example": 3600}}}, "CreateEntityData": {"type": "object", "required": ["name", "defaultCountry", "defaultCurrency", "defaultLanguage", "jurisdictionCode", "webSiteUrl"], "properties": {"name": {"type": "string", "description": "Entity name", "example": "ENTITY1"}, "type": {"type": "string", "description": "Entity type, possible values: entity, brand", "example": "brand"}, "description": {"type": "string", "description": "Entity description", "example": "Test entity"}, "title": {"type": "string", "description": "Entity title", "example": "Main entity"}, "defaultCurrency": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "defaultCountry": {"type": "string", "description": "country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "defaultLanguage": {"type": "string", "description": "language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "languages": {"type": "array", "description": "list of available languages codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "items": {"type": "string"}, "example": ["en", "zh"]}, "domain": {"type": "string", "description": "domain from which users can login to BO", "example": "bo.gc.skywind-tech.com"}, "merchantTypes": {"type": "array", "description": "list of available merchant types for entity", "items": {"type": "string"}, "example": ["ipm", "mrch_json", "pop_moorgate"]}, "merchantTypesInherited": {"type": "boolean", "description": "flag which shows is merchant types should be inherited from parent", "example": true}, "deploymentGroupRoute": {"type": "string", "description": "deployment group route", "example": "italy"}, "jurisdictionCode": {"type": "string", "description": "Jurisdiction code", "example": "COM"}, "webSiteUrl": {"type": "string", "description": "Web site url", "example": "http://web-site.com"}}}, "CreateEntityDataForBrandOrMerchant": {"type": "object", "required": ["name", "defaultCountry", "defaultCurrency", "defaultLanguage", "jurisdictionCode", "webSiteUrl"], "properties": {"name": {"type": "string", "description": "Entity name", "example": "ENTITY1"}, "description": {"type": "string", "description": "Entity description", "example": "Test entity"}, "title": {"type": "string", "description": "Entity title", "example": "Main entity"}, "defaultCurrency": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "defaultCountry": {"type": "string", "description": "country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "defaultLanguage": {"type": "string", "description": "language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "languages": {"type": "array", "description": "list of available languages codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "items": {"type": "string"}, "example": ["en", "zh"]}, "domain": {"type": "string", "description": "domain from which users can login to BO", "example": "bo.gc.skywind-tech.com"}, "jurisdictionCode": {"type": "string", "description": "Jurisdiction code", "example": "COM"}, "webSiteUrl": {"type": "string", "description": "Web site url", "example": "http://web-site.com"}}}, "CreateMerchantEntityData": {"allOf": [{"$ref": "#/definitions/CreateEntityDataForBrandOrMerchant"}, {"$ref": "#/definitions/Merchant"}, {"type": "object"}]}, "CreateBrandEntityData": {"allOf": [{"$ref": "#/definitions/CreateEntityDataForBrandOrMerchant"}, {"$ref": "#/definitions/Brand"}, {"type": "object"}]}, "UpdateEntityData": {"type": "object", "properties": {"status": {"type": "string", "description": "status of item (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "description": {"type": "string", "description": "Entity description", "example": "Test entity"}, "title": {"type": "string", "description": "Entity title", "example": "Main entity"}, "defaultCurrency": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "currencies": {"type": "array", "description": "list of available currencies codes [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "items": {"type": "string"}, "example": ["USD", "CNY"]}, "defaultCountry": {"type": "string", "description": "country code (ISO 3166-1 alpha-2)", "example": "US"}, "countries": {"type": "array", "description": "list of available countries codes [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "items": {"type": "string"}, "example": ["US", "CN"]}, "defaultLanguage": {"type": "string", "description": "language code (ISO 639-1)", "example": "en"}, "languages": {"type": "array", "description": "list of available languages codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "items": {"type": "string"}, "example": ["en", "zh"]}, "domain": {"type": "string", "description": "domain from which users can login to BO", "example": "bo.gc.skywind-tech.com"}, "merchantTypes": {"type": "array", "description": "list of available merchant types for entity", "items": {"type": "string"}, "example": ["ipm", "mrch_json", "pop_moorgate"]}, "deploymentGroupRoute": {"type": "string", "description": "deployment group route", "example": "italy"}}}, "UpdateEntityDataForBrandOrMerchant": {"type": "object", "properties": {"status": {"type": "string", "description": "status of item (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "description": {"type": "string", "description": "Entity description", "example": "Test entity"}, "title": {"type": "string", "description": "Entity title", "example": "Main entity"}, "defaultCurrency": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "currencies": {"type": "array", "description": "list of available currencies codes [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "items": {"type": "string"}, "example": ["USD", "CNY"]}, "defaultCountry": {"type": "string", "description": "country code (ISO 3166-1 alpha-2)", "example": "US"}, "countries": {"type": "array", "description": "list of available countries codes [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "items": {"type": "string"}, "example": ["US", "CN"]}, "defaultLanguage": {"type": "string", "description": "language code (ISO 639-1)", "example": "en"}, "languages": {"type": "array", "description": "list of available languages codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "items": {"type": "string"}, "example": ["en", "zh"]}, "domain": {"type": "string", "description": "domain from which users can login to BO", "example": "bo.gc.skywind-tech.com"}}}, "UpdateMerchantEntityData": {"allOf": [{"$ref": "#/definitions/UpdateEntityDataForBrandOrMerchant"}, {"$ref": "#/definitions/UpdateMerchantData"}, {"type": "object"}]}, "CreatePlayerData": {"type": "object", "required": ["code"], "properties": {"code": {"type": "string", "description": "player code", "example": "PL0001"}, "firstName": {"type": "string", "description": "player first name", "example": "Name"}, "lastName": {"type": "string", "description": "player last name", "example": "Surname"}, "email": {"type": "string", "description": "player email", "example": "<EMAIL>"}, "country": {"type": "string", "description": "player country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "currency": {"type": "string", "description": "player currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "isTest": {"type": "boolean", "description": "user for testing only", "example": false}, "language": {"type": "string", "description": "player language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "status": {"type": "string", "description": "player status", "example": "normal"}, "customData": {"type": "object", "description": "custom player's data", "example": [{"key": "league", "keyName": "faction", "value": "Noasauridae"}]}, "gameGroup": {"type": "string", "description": "game definitions name", "example": "VIP-1"}, "responsibleGamingSettings": {"type": "object", "properties": {"casino": {"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}, "sport_bet": {"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}}}, "comments": {"type": "string", "description": "Customers are be able to same any text information there", "example": "Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."}, "deactivatedAt": {"type": "string", "description": "Datetime when player should be deactivated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}}}, "UpdatePlayerPasswordData": {"type": "object", "required": ["newPassword", "confirmPassword"], "properties": {"oldPassword": {"type": "string", "description": "player's old password", "example": "19Letters&4Numbers&3Signs!"}, "newPassword": {"type": "string", "description": "player's new password", "example": "19Letters&4Numbers&3Signs!!"}, "confirmPassword": {"type": "string", "description": "confirmation of the correct spelling of the new password", "example": "19Letters&4Numbers&3Signs!!"}}}, "RegisterPlayerData": {"type": "object", "required": ["code", "password"], "properties": {"code": {"type": "string", "description": "player code", "example": "PL0001"}, "firstName": {"type": "string", "description": "player first name", "example": "Name"}, "lastName": {"type": "string", "description": "player last name", "example": "Surname"}, "nickname": {"type": "string", "description": "player nickname", "example": "Nickname"}, "email": {"type": "string", "description": "player email", "example": "<EMAIL>"}, "country": {"type": "string", "description": "player country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "currency": {"type": "string", "description": "player currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "isTest": {"type": "boolean", "description": "user for testing only", "example": false}, "language": {"type": "string", "description": "player language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "status": {"type": "string", "description": "player status", "example": "normal"}, "password": {"type": "string", "format": "password", "description": "password is longer than or equal 8 letters, is no longer than or equal 255 and contains at least one letter, one uppercase letter and one digit", "minLength": 8, "maxLength": 255, "example": "Big!Secret01"}, "customData": {"type": "object", "description": "custom player's data", "example": [{"key": "league", "keyName": "faction", "value": "Noasauridae"}]}, "gameGroup": {"type": "string", "description": "game group name", "example": "VIP-1"}, "agentDomain": {"type": "string", "description": "Domain address for agent's domen", "example": "example.com"}, "responsibleGamingSettings": {"type": "object", "properties": {"casino": {"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}, "sport_bet": {"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}}}, "comments": {"type": "string", "description": "Customers are be able to same any text information there", "example": "Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."}, "deactivatedAt": {"type": "string", "description": "Datetime when player should be deactivated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}}}, "UpdatePlayerData": {"type": "object", "properties": {"firstName": {"type": "string", "description": "player first name", "example": "Name"}, "lastName": {"type": "string", "description": "player last name", "example": "Surname"}, "nickname": {"type": "string", "description": "player nickname", "example": "Nickname"}, "email": {"type": "string", "description": "player email", "example": "<EMAIL>"}, "country": {"type": "string", "description": "player country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "currency": {"type": "string", "description": "player currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "isTest": {"type": "boolean", "description": "user for testing only", "example": false}, "language": {"type": "string", "description": "player language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "status": {"type": "string", "description": "player status", "example": "normal"}, "customData": {"type": "object", "description": "custom player's data", "example": [{"key": "league", "keyName": "faction", "value": "Noasauridae"}]}, "gameGroup": {"type": "string", "description": "game group name", "example": "VIP-1"}, "comments": {"type": "string", "description": "Customers are be able to same any text information there", "example": "Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."}, "deactivatedAt": {"type": "string", "description": "Datetime when player should be deactivated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}}}, "CreateUserData": {"type": "object", "required": ["username", "password", "email"], "properties": {"username": {"type": "string", "description": "user name", "example": "USER1"}, "password": {"type": "string", "format": "password", "description": "Password policy: longer than or equal 8 letters, no longer than or equal 255 and contains at least one letter, one uppercase letter and one digit", "minLength": 8, "maxLength": 255, "example": "123456qaB"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+4235672234324"}, "userType": {"type": "string", "description": "Type of the user (bo, operator_api, studio_user)", "enum": ["bo", "operator_api", "studio_user"], "example": "bo"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/SummaryRoleSchema"}}, "forcePasswordChangePeriodType": {"type": "string", "description": "Type of the period (minutely, hourly, daily, weekly, monthly, yearly)", "enum": ["minutely", "hourly", "daily", "weekly", "monthly", "yearly"], "example": "monthly"}, "forcePasswordChangePeriod": {"type": "number", "example": 5}, "customData": {"type": "object"}}}, "GameServerSettingsData": {"type": "object", "required": ["name", "roundIdRange", "sessionIdRange"], "properties": {"name": {"type": "string", "description": "Game server name", "example": "gs1"}, "description": {"type": "string", "description": "Description", "example": "Some description"}, "roundIdRange": {"type": "array", "items": {"type": "string"}, "example": ["0", "100000"]}, "sessionIdRange": {"type": "array", "items": {"type": "string"}, "example": ["0", "100000"]}}}, "GameServerSettingsUpdateData": {"type": "object", "properties": {"name": {"type": "string", "description": "Game server name", "example": "gs1"}, "description": {"type": "string", "description": "Description", "example": "Some description"}, "roundIdRange": {"type": "array", "items": {"type": "string"}, "example": ["0", "100000"]}, "sessionIdRange": {"type": "array", "items": {"type": "string"}, "example": ["0", "100000"]}}}, "ChangeEntityGameData": {"type": "object", "properties": {"status": {"type": "string", "description": "status of item (normal/suspended)", "enum": ["normal", "suspended", "test", "hidden"], "example": "normal"}, "settings": {"$ref": "#/definitions/EntityGameSettings", "description": "game settings - any key-value pairs"}, "limitFilters": {"$ref": "#/definitions/LimitFiltersByCurrencyCode", "description": "Limit filters"}, "urlParams": {"$ref": "#/definitions/EntityGameUrlParams", "description": "Entity game url params"}, "externalGameId": {"type": "string", "description": "The external game id of the operator"}, "domain": {"type": "string", "description": "Static domain"}}}, "LimitFiltersByCurrencyCode": {"type": "object", "description": "Staked games only", "properties": {"currencyCode": {"type": "object", "description": "currency code", "properties": {"stakeMax": {"type": "integer", "description": "the maximum value from the stakeAll array"}, "stakeMin": {"type": "integer", "description": "the minimum value from the stakeAll array"}}}}, "example": {"USD": {"stakeMax": 10, "stakeMin": 2}}}, "EntityGameUrlParams": {"type": "object", "description": "Params to append in game url, specified per entity game", "example": {"modules": "balance,pop", "balance_idle": 1, "balance_ping": 30, "disableBalancePing": true, "socketVersion": "4"}}, "GameCodesData": {"type": "object", "required": ["codes"], "properties": {"codes": {"type": "array", "description": "list of game codes", "items": {"type": "string"}, "example": ["sw_gol", "sw_fufish"]}}}, "AgentGroupStatus": {"type": "object", "required": ["status", "id"], "properties": {"status": {"type": "string", "description": "new status", "example": "normal", "enum": ["normal", "suspended"]}, "id": {"type": "array", "description": "list of agent's public ids", "items": {"type": "string"}, "example": ["7Lg8Z3d5", "7LgZZjXa"]}}}, "PlayerGroupStatus": {"type": "object", "required": ["status", "id"], "properties": {"status": {"type": "string", "description": "new status", "example": "normal", "enum": ["normal", "suspended"]}, "id": {"type": "array", "description": "list of player's public ids", "items": {"type": "string"}, "example": ["7Lg8Z3d5", "7LgZZjXa"]}}}, "GameGroupStatus": {"type": "object", "required": ["status", "codes"], "properties": {"status": {"type": "string", "description": "new status", "example": "normal", "enum": ["normal", "suspended"]}, "codes": {"type": "array", "description": "list of game codes", "items": {"type": "string"}, "example": ["sw_gol", "sw_fufish"]}, "allowToFinishCurrentSession": {"type": "boolean", "description": "Allow players to finish active session in case of suspending game. Default false.", "example": false}}}, "GameGroupLimitFilters": {"type": "object", "required": ["limitFilters", "codes"], "properties": {"limitFilters": {"$ref": "#/definitions/LimitFiltersByCurrencyCode", "description": "Limit filters"}, "codes": {"type": "array", "description": "list of game codes", "items": {"type": "string"}, "example": ["sw_gol", "sw_fufish"]}, "merge": {"type": "boolean", "description": "Set parameter to true if need to update limit filters with deep merge. It is mean that limit filter will be merge with parent or with exist limit filter. Set parameter to false if you want to overwrite all previous filters.", "example": false}}}, "UserGroupStatus": {"type": "object", "required": ["status", "id"], "properties": {"status": {"type": "string", "description": "new status", "example": "normal", "enum": ["normal", "suspended"]}, "id": {"type": "array", "description": "list of user's public ids", "items": {"type": "string"}, "example": ["7Lg8Z3d5", "7LgZZjXa"]}}}, "NotificationInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "notification public id", "example": "N6wZDj5a"}, "message": {"type": "string", "description": "full text of message", "example": "Today we are going to migrate to new server. Save all changes and be ready"}, "ts": {"type": "string", "description": "timestamp when notification has been created (ISO 8601 timestamp)", "example": "2017-02-16T16:37:13.613Z"}, "status": {"type": "string", "description": "payment method status", "example": "suspended"}, "unread": {"type": "boolean", "description": "is message unread before by user", "example": true}, "authorId": {"type": "string", "description": "id of user-sender", "example": "7LgZZjXa"}}}, "NotificationShortInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "notification public id", "example": "N6wZDj5a"}, "message": {"type": "string", "description": "full text of message", "example": "Today we are going to migrate to new server. Save all changes and be ready"}, "ts": {"type": "string", "description": "timestamp when notification has been created (ISO 8601 timestamp)", "example": "2017-02-16T16:37:13.613Z"}, "status": {"type": "string", "description": "payment method status", "example": "suspended"}}}, "ExternalReference": {"type": "object", "properties": {"exTrxId": {"type": "number", "description": "external reference", "example": 1234567}}}, "PaymentInfo": {"type": "object", "properties": {"type": {"type": "string", "description": "type of payment method", "example": "deposit"}, "code": {"type": "string", "description": "payment method code", "example": "paypal"}, "name": {"type": "string", "description": "payment method name", "example": "PayPal"}, "description": {"type": "string", "description": "description of payment method", "example": "the best payment method in the world"}, "status": {"type": "string", "description": "payment method status", "example": "suspended"}}}, "PaymentMethodDetails": {"type": "object", "required": ["paymentMethodCode", "currency", "customerId"], "properties": {"paymentMethodCode": {"type": "string", "description": "Payment Method Code", "example": "PayPal"}, "customerId": {"type": "string", "description": "Player code", "example": "PL00001"}, "amount": {"type": "number", "description": "Payment amount", "example": 10000}, "currency": {"type": "string", "description": "currency name", "example": "USD"}, "isTest": {"type": "boolean", "description": "Is test data", "example": false}}}, "PaymentOrderInfo": {"type": "object", "properties": {"trxId": {"type": "string"}, "brandId": {"type": "string"}, "brandTitle": {"type": "string"}, "playerCode": {"type": "string"}, "orderId": {"type": "string"}, "orderType": {"type": "string"}, "orderDate": {"type": "string"}, "orderInfo": {"type": "string"}, "orderStatus": {"type": "string"}, "currencyCode": {"type": "string"}, "amount": {"type": "number"}, "playerBalanceAfter": {"type": "number"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "paymentMethodCode": {"type": "string"}, "isTest": {"type": "boolean"}, "extTrxId": {"type": "string"}}}, "TransferDataInfo": {"type": "object", "required": ["playerCode", "currency", "amount"], "properties": {"playerCode": {"type": "string", "example": "PL0001"}, "currency": {"type": "string", "example": "USD"}, "amount": {"type": "number", "example": 100}, "extTrxId": {"type": "string", "example": "fuibawd198fh1-1u2bd91-12uhehb"}, "isTest": {"type": "boolean", "example": true}}}, "PaymentMethodType": {}, "ProviderGameCode": {"type": "string", "description": "Game code", "example": "GAME001"}, "ProviderId": {"type": "string", "description": "Provider id", "example": "12Qwer2-56yu-1213gh"}, "SiteToken": {"type": "object", "required": ["token", "id"], "properties": {"token": {"type": "string", "description": "site token", "example": "ed2783jdnsjnjwej82edhjzaqlgs"}, "id": {"type": "string", "description": "public id of site token", "example": "7Hf9kS7M"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Re76K9k0"}}}, "SchemaDefinitionId": {"type": "string", "description": "Reference to the schema with description of format and permissions for game limits fields.", "example": "W4RkGRen"}, "TotalBetMultiplier": {"type": "number", "description": "Total bet multiplier", "example": 1}, "GameUpdateData": {"type": "object", "description": "Game update information", "required": ["title", "url", "defaultInfo", "info", "limits"], "properties": {"type": {"type": "string", "enum": ["slot", "action", "table"], "description": "Game type", "example": "slot"}, "title": {"type": "string", "description": "Game title", "example": "Updated-Name-For-Super-Game"}, "url": {"type": "string", "description": "Game URL", "example": "http://api.test/{clientVersion}/game/"}, "defaultClientVersion": {"type": "string", "description": "Game client version, will be used if placeholder {clientVersion} is presented in url", "example": "1.2.3"}, "historyUrl": {"type": "string", "description": "Game history URL", "example": "http://api.test/history.html"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"$ref": "#/definitions/GameDescriptionByLocale"}, "limits": {"$ref": "#/definitions/ProviderLimitsByCurrencyCode"}, "settings": {"$ref": "#/definitions/GameSettings"}, "features": {"$ref": "#/definitions/GameFeatures"}, "historyRenderType": {"$ref": "#/definitions/GameHistoryRenderType"}, "providerGameCode": {"type": "string", "example": "sw_provider"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}, "clientFeatures": {"$ref": "#/definitions/ClientFeatures"}, "physicalTableId": {"type": "string", "description": "Physical table id. if set null, physical table id will remove", "example": "pQ3513OE"}, "isMergeLiveSettings": {"type": "boolean", "description": "if true, the merge mechanism is enabled for live settings in features object", "example": false}, "providerId": {"type": "string", "description": "The public ID of the new game provider to be set", "example": "W4RkGRen"}}}, "GameFeatures": {"type": "object", "description": "Game features", "properties": {"transferEnabled": {"type": "boolean", "description": "Transfer in/out enabled flag", "example": false}, "jackpotTypes": {"type": "array", "items": {"type": "string"}, "description": "Array of jackpot types required for the game", "example": ["sw-jpgame"]}, "ignoreJackpotTypesValidation": {"type": "boolean", "description": "Enable or disabled jackpot types validation on entityGame level", "example": false}, "translations": {"type": "object", "description": "translations for title and description", "properties": {"en": {"$ref": "#/definitions/GameFeaturesTranslationItem"}}}, "isFreebetSupported": {"type": "boolean", "description": "true if game supports freebets", "example": false}, "isMultibet": {"type": "boolean", "description": "true if game supports multi-bets", "example": false}, "allowMissingContribution": {"type": "boolean", "description": "true if game supports missing contribution", "example": false}, "isCustomLimitsSupported": {"type": "boolean", "description": "true if game limits updating for old limits system is allowed, used by BO", "example": false}, "decreaseMaxBetSupported": {"type": "boolean", "description": "true if custom game group limit filters is allowed to decrease max bet", "example": false}, "increaseMinBetSupported": {"type": "boolean", "description": "true if custom game group limit filters is allowed to increase min bet", "example": false}, "isFunModeNotSupported": {"type": "boolean", "description": "true if game can only be played in real money mode", "example": false}, "isBonusCoinsSupported": {"type": "boolean", "description": "true if game supports bonus coins", "example": false}, "isMarketplaceSupported": {"type": "boolean", "description": "true if dame supports creating game limit configurations via marketplace", "example": false}, "isGRCGame": {"type": "boolean", "description": "true if this is retention tool game", "example": false}, "gamble": {"type": "boolean", "description": "true will correspond to ON Gamble feature", "example": false}, "baseRTP": {"type": "number", "description": "expected game RTP", "example": 95}, "highestPrizeProbability": {"type": "number", "description": "Highest prize probability. German regulation", "example": 100}, "highestWin": {"type": "number", "description": "Highest win (in EUR). We use it in game group filters for calculating maxTotalBet from maxExposure.", "example": 10000}, "offlineWin": {"type": "boolean", "description": "When true, indicates that the win will happen probably after session expiration", "example": false}, "baseRTPRange": {"type": "object", "properties": {"min": {"type": "number", "example": 90.42}, "max": {"type": "number", "example": 93.52}}, "description": "expected game RTP range, used only by marketplace"}, "jpRTP": {"type": "number", "description": "expected game RTP with jackpot feature", "example": 95}, "supportsMarketingJP": {"type": "boolean", "description": "true if game supports contributions to marketing jackpots", "example": false}, "supportsRtpConfigurator": {"type": "boolean", "description": "true if game support RTP deduction configuration", "example": false}, "currenciesSupport": {"type": "array", "items": {"type": "string"}, "description": "The array of supported currency for the game", "example": ["EUR"]}, "gameFinalizationType": {"type": "string", "enum": ["none", "finalization", "autoPlay"], "description": "Supported by game module finalization type: none -  game doesn't support finalization, finalization - game module has \"finalizeGame\" method, autoPlay - game supports auto play ", "example": "none"}, "live": {"$ref": "#/definitions/GameFeatures"}, "featuresRTP": {"type": "object", "description": "RTP of each feature/mode", "properties": {"mode1": {"$ref": "#/definitions/FeatureRTP"}, "mode2": {"$ref": "#/definitions/FeatureRTP"}, "freeGames": {"$ref": "#/definitions/FeatureRTP"}}}, "validateRequestsExtensionEnabled": {"type": "boolean", "description": "true if game is compatible with request validation extension", "example": true}, "zeroBetCheckEnabled": {"type": "boolean", "description": "enable check for zero bet for this game", "example": false}, "instantJpEnabled": {"type": "boolean", "description": "true if game supports Instant JP (<PERSON>)", "example": false}, "supportsReplay": {"type": "boolean", "description": "true if the game supports replay mode", "example": false}, "gameLimitsSettings": {"type": "object", "additionalProperties": {"type": "object", "properties": {"copyLimitsFrom": {"type": "string", "description": "Currency from which limits can be copied", "example": "CLP"}}}, "example": {"USD": {"copyLimitsFrom": "CLP"}}}}}, "LiveTable": {"type": "object", "required": ["tableId", "provider", "gameCode", "rushOrder"], "properties": {"tableId": {"type": "string"}, "tableName": {"type": "string"}, "provider": {"type": "string"}, "providerSettings": {"type": "object"}, "selectionIntervalAfterNoMoreBets": {"type": "number", "description": "Criteria to select table for live rush game", "example": 3000}, "selectionIntervalBeforeNoMoreBets": {"type": "number", "description": "Criteria to select table for live rush game", "example": 5000}, "gameCode": {"type": "string", "description": "Game code to contain in live rush game"}, "rushOrder": {"type": "number", "description": "Rush order", "example": 1}}}, "Live": {"type": "object", "allOf": [{"$ref": "#/definitions/LiveTable"}], "properties": {"type": {"type": "string", "enum": ["baccarat", "roulette", "blackjack"], "description": "Type of live rush games", "example": "baccarat"}, "tables": {"type": "array", "description": "Live tables", "items": {"$ref": "#/definitions/LiveTable"}}}}, "GameFeaturesTranslationItem": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "example": "titleEN"}, "description": {"type": "string", "example": "description in EN"}}}, "GameCategoriesTranslationItem": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "example": "title EU"}, "description": {"type": "string", "example": "description in EU"}, "icon": {"type": "string", "example": "icon in EU"}}}, "GameJackpots": {"type": "object", "description": "jackports of game", "additionalProperties": {"type": "object", "description": "jackpot info for game", "properties": {"currency": {"type": "string", "example": "USD"}, "id": {"type": "string", "example": "sw-jpgame"}, "pools": {"type": "object", "additionalProperties": {"type": "object", "properties": {"amount": {"type": "number", "example": 120}}}}}}, "example": {"sw-jpgame": {"currency": "USD", "id": "sw-jpgame", "pools": {"pool0": {"amount": 122}}}}}, "GameLive": {"type": "object", "description": "live info for game", "properties": {"id": {"type": "string", "description": "table id", "example": "mock-0-1"}, "provider": {"type": "string", "description": "table provider", "example": "mock"}, "dealer": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON><PERSON>"}, "picture": {"type": "string", "example": "http://picture.com/mock.jpeg"}}}, "status": {"type": "string", "description": "table status", "example": "online"}, "type": {"type": "number", "description": "table game type", "example": 0}}, "example": {"id": "mock-0-1", "provider": "mock", "dealer": {"name": "<PERSON><PERSON>", "picture": "http://picture.com/mock.jpeg"}, "status": "online", "type": 0}}, "GameRegistration": {"type": "object", "description": "Game registration information", "required": ["gameCode", "providerGameCode", "title", "type", "url", "defaultInfo", "info", "limits", "providerId"], "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "GAME001"}, "providerGameCode": {"$ref": "#/definitions/ProviderGameCode"}, "title": {"type": "string", "description": "Game title", "example": "Game 1"}, "type": {"type": "string", "enum": ["slot", "action", "table", "live"], "description": "Game type", "example": "slot"}, "url": {"type": "string", "description": "Game URL", "example": "http://api.test/game"}, "historyUrl": {"type": "string", "description": "Game history url", "example": "http://api.test/index.html"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"$ref": "#/definitions/GameDescriptionByLocale"}, "limits": {"$ref": "#/definitions/ProviderLimitsByCurrencyCode"}, "providerId": {"$ref": "#/definitions/ProviderId"}, "settings": {"$ref": "#/definitions/GameSettings"}, "features": {"$ref": "#/definitions/GameFeatures"}, "historyRenderType": {"$ref": "#/definitions/GameHistoryRenderType"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}, "clientFeatures": {"$ref": "#/definitions/ClientFeatures"}, "physicalTableId": {"type": "string", "description": "Physical table id", "example": "pQ3513OE"}}}, "SlotGameRegistration": {"type": "object", "description": "Slot game registration information", "required": ["gameCode", "providerGameCode", "title", "url", "defaultInfo", "info", "limits", "providerId"], "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "GAME001"}, "providerGameCode": {"$ref": "#/definitions/ProviderGameCode"}, "title": {"type": "string", "description": "Game title", "example": "Game 1"}, "url": {"type": "string", "description": "Game URL", "example": "http://api.test/game"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"$ref": "#/definitions/GameDescriptionByLocale"}, "limits": {"$ref": "#/definitions/SlotGameLimitsByCurrencyCode"}, "providerId": {"$ref": "#/definitions/ProviderId"}, "settings": {"$ref": "#/definitions/GameSettings"}, "features": {"$ref": "#/definitions/GameFeatures"}, "historyRenderType": {"$ref": "#/definitions/GameHistoryRenderType"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}}}, "ActionGameRegistration": {"type": "object", "description": "Action game registration information", "required": ["gameCode", "providerGameCode", "title", "url", "defaultInfo", "info", "limits", "providerId"], "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "GAME001"}, "providerGameCode": {"$ref": "#/definitions/ProviderGameCode"}, "title": {"type": "string", "description": "Game title", "example": "Game 1"}, "url": {"type": "string", "description": "Game URL", "example": "http://api.test/game"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"$ref": "#/definitions/GameDescriptionByLocale"}, "limits": {"$ref": "#/definitions/ActionGameLimitsByCurrencyCode"}, "providerId": {"$ref": "#/definitions/ProviderId"}, "settings": {"$ref": "#/definitions/GameSettings"}, "features": {"$ref": "#/definitions/GameFeatures"}, "historyRenderType": {"$ref": "#/definitions/GameHistoryRenderType"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}}}, "TableGameRegistration": {"type": "object", "description": "Table game registration information", "required": ["gameCode", "providerGameCode", "title", "url", "defaultInfo", "info", "limits", "providerId"], "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "GAME001"}, "providerGameCode": {"$ref": "#/definitions/ProviderGameCode"}, "title": {"type": "string", "description": "Game title", "example": "Game 1"}, "url": {"type": "string", "description": "Game URL", "example": "http://api.test/game"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"$ref": "#/definitions/GameDescriptionByLocale"}, "limits": {"$ref": "#/definitions/TableGameLimitsByCurrencyCode"}, "providerId": {"$ref": "#/definitions/ProviderId"}, "settings": {"$ref": "#/definitions/GameSettings"}, "features": {"$ref": "#/definitions/GameFeatures"}, "historyRenderType": {"$ref": "#/definitions/GameHistoryRenderType"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}}}, "LiveGameRegistration": {"type": "object", "description": "Table game registration information", "required": ["gameCode", "providerGameCode", "title", "url", "defaultInfo", "info", "limits", "providerId"], "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "GAME001"}, "providerGameCode": {"$ref": "#/definitions/ProviderGameCode"}, "title": {"type": "string", "description": "Game title", "example": "Game 1"}, "url": {"type": "string", "description": "Game URL", "example": "http://api.test/game"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"$ref": "#/definitions/GameDescriptionByLocale"}, "limits": {"$ref": "#/definitions/TableGameLimitsByCurrencyCode"}, "providerId": {"$ref": "#/definitions/ProviderId"}, "settings": {"$ref": "#/definitions/GameSettings"}, "features": {"$ref": "#/definitions/GameFeatures"}, "historyRenderType": {"$ref": "#/definitions/GameHistoryRenderType"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}, "physicalTableId": {"type": "string", "description": "Physical table id", "example": "pQ3513OE"}}}, "ExternalGameRegistration": {"type": "object", "description": "External game registration information", "required": ["gameCode", "providerGameCode", "title", "defaultInfo", "info", "providerId"], "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "GAME001"}, "providerGameCode": {"$ref": "#/definitions/ProviderGameCode"}, "title": {"type": "string", "description": "Game title", "example": "Game 1"}, "url": {"type": "string", "description": "Game URL", "example": "http://api.test/game"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"$ref": "#/definitions/GameDescriptionByLocale"}, "providerId": {"$ref": "#/definitions/ProviderId"}, "settings": {"$ref": "#/definitions/GameSettings"}, "historyRenderType": {"$ref": "#/definitions/GameHistoryRenderType"}, "limitsGroup": {"$ref": "#/definitions/limitsGroup"}, "countries": {"$ref": "#/definitions/CountryCodes"}, "totalBetMultiplier": {"$ref": "#/definitions/TotalBetMultiplier"}, "schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}}}, "GameDescriptionByLocale": {"type": "object", "description": "Game description per locale", "additionalProperties": {"$ref": "#/definitions/GameDescription"}, "example": {"USD": {"name": "Game name", "description": "Game description"}}}, "LabelList": {"type": "array", "description": "Label list array", "items": {"$ref": "#/definitions/CreateLabelInfo"}}, "LimitsObject": {"type": "object", "description": "Limits"}, "SlotGameLimits": {"allOf": [{"$ref": "#/definitions/LimitsObject"}, {"type": "object", "required": ["maxTotalStake", "stakeAll", "stakeDef", "stakeMax", "stakeMin", "winMax"], "properties": {"maxTotalStake": {"type": "number", "description": "Maximal total stake", "example": 1000}, "stakeAll": {"type": "array", "description": "All possible stakes", "items": {"type": "number"}, "example": [0.1, 0.5, 1, 2, 3, 5]}, "items": {"type": "number"}, "stakeDef": {"type": "number", "description": "Default stake", "example": 1}, "stakeMax": {"type": "number", "description": "Maximal stake", "example": 5}, "stakeMin": {"type": "number", "description": "Minimal stake", "example": 0.1}, "winMax": {"type": "number", "description": "Maximal win", "example": 2000}}}]}, "TableGameLimits": {"allOf": [{"$ref": "#/definitions/LimitsObject"}, {"type": "object", "required": ["chipAll", "stakeDef", "stakeMax", "stakeMin"], "properties": {"chipAll": {"type": "array", "description": "All possible chip list", "items": {"type": "number"}, "example": [0.1, 0.5, 1, 2, 3, 5]}, "items": {"type": "number"}, "stakeDef": {"type": "number", "description": "Default stake", "example": 1}, "stakeMax": {"type": "number", "description": "Maximal stake", "example": 5}, "stakeMin": {"type": "number", "description": "Minimal stake", "example": 0.1}}}]}, "ActionGameLimits": {"allOf": [{"$ref": "#/definitions/LimitsObject"}, {"type": "object", "required": ["coinsRate"], "properties": {"coinsRate": {"type": "number", "description": "Coins rate", "example": 0.01}, "stakeAll": {"type": "array", "description": "all possible stake", "items": {"type": "number"}}, "items": {"type": "number", "example": [0.1, 0.5, 1, 2, 3, 5]}}}]}, "ProviderLimitsByCurrencyCode": {"type": "object", "description": "Limits per currency", "additionalProperties": {"$ref": "#/definitions/LimitsObject"}}, "SlotGameLimitsByCurrencyCode": {"type": "object", "description": "Slot games limits per currency", "additionalProperties": {"$ref": "#/definitions/SlotGameLimits"}, "example": {"USD": {"maxTotalStake": 200, "stakeAll": [2, 3, 5, 10], "stakeDef": 2, "stakeMax": 10, "stakeMin": 2, "winMax": 400}}}, "ActionGameLimitsByCurrencyCode": {"type": "object", "description": "Action games limits per currency", "additionalProperties": {"$ref": "#/definitions/ActionGameLimits"}, "example": {"USD": {"coinsRate": 0.01, "stakeAll": [0.01, 0.02, 0.3]}, "CNY": {"coinsRate": 0.1, "stakeAll": [0.1, 0.11, 0.12]}}}, "TableGameLimitsByCurrencyCode": {"type": "object", "description": "Table games limits per currency", "additionalProperties": {"$ref": "#/definitions/TableGameLimits"}, "example": {"USD": {"low": {"stakeAll": [2, 3, 5, 10], "stakeDef": 2, "stakeMax": 10, "stakeMin": 2}, "mid": {"stakeAll": [4, 6, 10, 20], "stakeDef": 4, "stakeMax": 20, "stakeMin": 4}, "high": {"stakeAll": [8, 12, 20, 40], "stakeDef": 8, "stakeMax": 40, "stakeMin": 8}}}}, "CurrencyCode": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "Amount": {"type": "number", "description": "Payment amount", "example": 10000}, "TS": {"type": "string", "description": "The time of audit (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "isTest": {"type": "boolean", "description": "is game provider created only for testing", "example": true}, "CountriesArray": {"type": "object", "required": ["countries"], "properties": {"countries": {"type": "array", "description": "list of available countries codes [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "items": {"type": "string"}, "example": ["CN", "GB", "FR"]}}}, "CurrenciesArray": {"type": "object", "required": ["currencies"], "properties": {"currencies": {"type": "array", "description": "list of available currencies codes [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "items": {"type": "string"}, "example": ["USD", "CNY"]}}}, "LanguagesArray": {"type": "object", "required": ["languages"], "properties": {"languages": {"type": "array", "description": "list of available language codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "items": {"type": "string"}, "example": ["ak", "cs"]}}}, "SetPlayerPasswordData": {"type": "object", "required": ["newPassword", "confirmPassword"], "properties": {"newPassword": {"type": "string", "description": "player's new password", "example": "19Letters&4Numbers&3Signs!!"}, "confirmPassword": {"type": "string", "description": "confirmation of the correct spelling of the new password", "example": "19Letters&4Numbers&3Signs!!"}}}, "ExtWinBetHistoryEntry": {"type": "object", "description": "Win Bet history entry", "properties": {"extTrxId": {"type": "string", "description": "extTrxId generated by ext game provider", "example": "99167"}, "bet": {"type": "number", "description": "Bet amount. Null if win has not happened yet", "example": 10}, "win": {"type": "number", "description": "Win amount", "example": 20}, "currency": {"$ref": "#/definitions/CurrencyCode"}, "brandId": {"type": "string", "description": "Encoded brand id", "example": "Qa1weSD"}, "playerCode": {"type": "string", "description": "Player code", "example": "PL0001"}, "roundId": {"type": "number", "description": "Game id( round id)", "example": 167}, "gameCode": {"type": "string", "description": "Game code", "example": "pt_gm1"}, "gameProviderCode": {"type": "string", "description": "Game provider code", "example": "sw_gos"}, "balanceBefore": {"type": "number", "description": "Balance before bet", "example": 1000}, "balanceAfter": {"type": "number", "description": "Balance after win", "example": 1010}, "isTest": {"type": "boolean", "description": "True for fun games", "example": false}, "insertedAt": {"type": "string", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "example": "2017-12-10 16:00:00 UTC"}, "finished": {"type": "boolean", "description": "The state of the round: finished or unfinished", "example": true}, "eventId": {"type": "integer", "description": "The number of the spin in a sequence", "example": 0}, "recoveryType": {"type": "string", "description": "The recovery type: null, finalize or force-finish", "example": "force-finish"}}}, "ExtHistoryDetailsResponse": {"type": "object", "description": "Ext Bet Win history entry", "properties": {"result": {"type": "string", "description": "String from gameprovider with round details", "example": "<html>History</html>"}, "type": {"type": "string", "description": "Type of the result string. html or base64image", "example": "html"}}}, "JpContributionLog": {"type": "object", "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "pt-allad-reel-jp"}, "roundId": {"type": "string", "description": "Round ID", "example": 123}, "seedAmount": {"type": "number", "description": "Amount, contributed to the seed part of the JP", "example": 12.5}, "progressiveAmount": {"type": "number", "description": "Amount, contributed to the progressive part of the JP", "example": 6.5}, "contributionAmount": {"type": "number", "description": "Full bet amount of game stakes used for the Jackpot replenishment (in EUR)", "example": 6.5}, "currency": {"type": "string", "description": "currency", "example": "USD"}, "currencyRate": {"type": "number", "description": "currency rate", "example": 0.92123}, "insertedAt": {"type": "string", "description": "Inserted at", "example": "2018-01-03T17:00:00.000Z"}, "playerCode": {"type": "string", "description": "player code", "example": "test"}, "pool": {"type": "string", "description": "pool of contribute", "example": "mega"}, "trxDate": {"type": "string", "description": "transaction date", "example": "2018-01-03T17:00:00.000Z"}, "jackpotId": {"type": "string", "description": "jackpot id name", "example": "FIRE-reel"}}}, "JpWinLog": {"type": "object", "properties": {"playerCurrency": {"type": "string", "description": "Player Currency", "example": "EUR"}, "roundId": {"type": "string", "description": "Round ID", "example": 123}, "initialSeed": {"type": "number", "description": "Initial seed", "example": 100}, "externalId": {"type": "string", "description": "External ID", "example": "enOQKQAJew8AAALDenOQKSmDW10"}, "eventId": {"type": "number", "description": "Spin number", "example": 1}, "gameCode": {"type": "string", "description": "Game code", "example": "pt-allad-reel-jp"}, "seedAmount": {"type": "number", "description": "Amount, contributed to the seed part of the JP", "example": 12.5}, "progressiveAmount": {"type": "number", "description": "Amount, contributed to the progressive part of the JP", "example": 6.5}, "totalSeedAmount": {"type": "number", "description": "Total Amount, contributed to the seed part of the JP", "example": 12.5}, "totalProgressiveAmount": {"type": "number", "description": "Total Amount, contributed to the progressive part of the JP", "example": 6.5}, "winAmount": {"type": "number", "description": "Full win amount of game stakes used for the Jackpot replenishment (in EUR)", "example": 6.5}, "currency": {"type": "string", "description": "currency", "example": "USD"}, "currencyRate": {"type": "number", "description": "currency rate", "example": 0.92123}, "playerCode": {"type": "string", "description": "player code", "example": "test"}, "pool": {"type": "string", "description": "pool of contribute", "example": "mega"}, "trxDate": {"type": "string", "description": "transaction date", "example": "2018-01-03T17:00:00.000Z"}, "jackpotId": {"type": "string", "description": "jackpot id name", "example": "FIRE-reel"}}}, "limitsGroup": {"type": "string", "description": "criteria for grouping games with equal limits", "example": "slot50"}, "AutoPlaySettings": {"type": "object", "description": "auto play settings item", "properties": {"label": {"type": "string", "description": "label for autoplay item", "example": "50"}, "value": {"type": "number", "description": "number of spins for autoplay item", "example": 50}, "isUntilFeature": {"type": "boolean", "description": "is until feature (optional)", "example": true}, "isDefault": {"type": "boolean", "description": "is default option", "example": false}}}, "CountryCodes": {"type": "array", "description": "list of available countries codes [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). If empty - no restrictions", "items": {"type": "string"}, "example": ["CN", "GB", "FR"]}, "CountriesRestrictions": {"type": "array", "description": "list of allowed/forbidden countries codes [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). If country code starts with '!' this country consider as forbidden (blacklist), otherwise it's allowed(whitelist). If empty - no restrictions.", "items": {"type": "string"}, "example": ["CN", "GB", "FR"]}, "UpdateSchemaConfiguration": {"type": "object", "description": "Game limits schema configuration (default values)", "properties": {"name": {"type": "string", "example": "Slot Games default configuration", "description": "Name of schema configuration"}, "configuration": {"type": "object", "description": "Default values all fields are optional. All fields can have any name and type", "example": {"EUR": {"small": {"stakeAll": [0.1, 0.2, 0.5, 5], "defaultTotalStake": 1}, "big": {"stakeAll": [1, 2, 3, 50], "defaultTotalStake": 10}, "baccarat-room": {"bets": {"playerPair": {"max": 500, "exposure": 300000, "min": 1, "alert": 10, "block": 20}, "egalite": {"max": 25, "exposure": 15000, "min": 1}, "big": {"max": 250, "exposure": 150000, "min": 1}}}, "coins": [8, 18, 38, 88], "defaultCoin": 8, "stakeAll": [0.1, 0.2, 0.5, 5], "defaultTotalStake": 1, "winMax": 500000, "stakeDef": 1, "totalStakeMax": 2500, "totalStakeMin": 1, "alert": 10, "block": 20}, "CNY": {"small": {"stakeAll": [1, 2, 5, 50], "defaultTotalStake": 10}, "big": {"stakeAll": [10, 20, 30, 500], "defaultTotalStake": 100}}, "BNS": {"stakeAll": [1, 10, 50], "aligned": false}}}}}, "CreateSchemaConfiguration": {"allOf": [{"type": "object", "properties": {"schemaDefinitionId": {"$ref": "#/definitions/SchemaDefinitionId"}}}, {"$ref": "#/definitions/UpdateSchemaConfiguration"}]}, "SchemaConfiguration": {"allOf": [{"type": "object", "properties": {"id": {"type": "string", "description": "id of configuration", "example": "hu2s38S"}, "entityId": {"type": "string", "example": "hu2s38S", "description": "id of connected entity"}, "createdAt": {"type": "string", "description": "time when a record was created (ISO 8601 timestamp)", "example": "2016-02-23T12:45:42.324Z"}, "updatedAt": {"type": "string", "description": "last time a record was updated (ISO 8601 timestamp)", "example": "2017-03-08T05:15:54.213Z"}}}, {"$ref": "#/definitions/CreateSchemaConfiguration"}]}, "UpdateLimitTemplate": {"type": "object", "description": "Template for game limits", "properties": {"template": {"type": "object", "description": "Limits template value. Validation will be applied only on game limits creation", "example": {"small": {"stakeAll": [0.1, 0.2, 0.5, 5], "defaultTotalStake": 1}, "big": {"stakeAll": [1, 2, 3, 50], "defaultTotalStake": 10}, "baccarat-room": {"bets": {"playerPair": {"max": 500, "exposure": 300000, "min": 1, "alert": 10, "block": 20}, "egalite": {"max": 25, "exposure": 15000, "min": 1}, "big": {"max": 250, "exposure": 150000, "min": 1}}}, "coins": [8, 18, 38, 88], "defaultCoin": 8, "stakeAll": [0.1, 0.2, 0.5, 5], "defaultTotalStake": 1, "winMax": 500000, "stakeDef": 1, "totalStakeMax": 2500, "totalStakeMin": 1, "alert": 10, "block": 20}}}}, "CreateLimitTemplate": {"allOf": [{"type": "object", "description": "Template for game limits", "properties": {"name": {"type": "string", "example": "Slot Games default template", "description": "Name of limit template"}}}, {"$ref": "#/definitions/UpdateLimitTemplate"}]}, "LimitTemplate": {"allOf": [{"type": "object", "properties": {"id": {"type": "string", "description": "id of configuration", "example": "hu2s38S"}, "createdAt": {"type": "string", "description": "time when a record was created (ISO 8601 timestamp)", "example": "2016-02-23T12:45:42.324Z"}, "updatedAt": {"type": "string", "description": "last time a record was updated (ISO 8601 timestamp)", "example": "2017-03-08T05:15:54.213Z"}}}, {"$ref": "#/definitions/CreateSchemaConfiguration"}]}, "SetDomainTags": {"type": "object", "description": "Domain tags", "properties": {"tags": {"type": "array", "items": {"type": "string"}, "example": ["pokerstar.com"]}}}, "ClientFeatures": {"type": "object", "description": "Joins additional client features", "properties": {"fastPlay": {"type": "boolean", "description": "Triggered the next spin within less than 1 second of all reels stopping. False if absent", "example": true}, "turboPlus": {"type": "boolean", "description": "There is an additional faster turbo mode called Turbo+. False if absent", "example": true}, "turbo": {"type": "boolean", "description": "Is Turbo button enabled or disabled by default. (true - enabled)", "example": true}, "fullscreen": {"type": "boolean", "description": "", "example": true}, "disableTransferForBonusMode": {"type": "boolean", "description": "", "example": false}, "allowOppositeBets": {"type": "boolean", "description": "to disable opposite betting", "example": true}, "tableFullCoverage": {"type": "boolean", "description": "to disable opposite betting for Roulette", "example": true}, "maxBuyInStake": {"type": "number", "description": "max amount for buy in stake in EUR", "example": 2000}, "maxAnteBetStake": {"type": "number", "description": "max amount for ante bet stake in EUR", "example": 500}}}, "CheckWebSiteWhitelisted": {"type": "object", "description": "Flag that allows the system to check whether a website is whitelisted", "properties": {"level": {"type": "string", "description": "There are 3 leves: none, warning, error", "example": "warning"}}}, "DeploymentGroup": {"type": "object", "properties": {"id": {"type": "string", "description": "Deployment group public id", "example": "jJr70Mq1"}, "description": {"type": "string", "description": "Deployment group description", "example": "This is deployment group description"}, "route": {"type": "string", "description": "Deployment group route", "example": "live"}, "type": {"type": "string", "enum": ["game", "entity"], "description": "Deployment group type", "example": "game"}}}, "DeploymentGroupPost": {"type": "object", "properties": {"description": {"type": "string", "description": "Deployment group description", "example": "This is deployment group description"}, "route": {"type": "string", "description": "Deployment group route", "example": "live"}, "type": {"type": "string", "enum": ["game", "entity"], "description": "Deployment group type", "example": "game"}}}, "GameCodeByDeploymentGroup": {"type": "string", "description": "Game code", "example": "sw_al"}, "EntityPathByDeploymentGroup": {"type": "string", "description": "Entity path", "example": ":BRAND_ONE:Entity_2"}, "GameClientVersionsPerRoute": {"type": "object", "description": "Game client versions list grouper by deployment group route", "example": {"italy": {"clientVersion": "1.2.3", "clientFeatures": {}}, "spain": {"clientVersion": "1.2.4", "clientFeatures": {}}}}, "FavoriteGameInfo": {"type": "object", "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "sw_fufish"}, "count": {"type": "number", "description": "How many times the game has been opened", "example": 4}}}, "RecentlyGameInfo": {"type": "object", "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "sw_fufish"}, "updatedAt": {"type": "string", "description": "The last time a player played (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}}}, "DirectTransferDataInfo": {"allOf": [{"$ref": "#/definitions/TransferDataInfo"}, {"type": "object", "required": ["brandPath"], "properties": {"brandPath": {"type": "string", "example": "swftest_ENTITY:Yuliya:Yuliya_brand", "description": "path(relative from key-entity) for the brand of the player "}}}]}, "FeatureRTP": {"type": "object", "required": ["rtpReducer", "RTP"], "properties": {"rtpReducer": {"type": "boolean", "example": true, "description": "reduce RTP of feature when reducer enabled"}, "RTP": {"type": "number", "example": 95.75, "description": "RTP of feature"}}}, "EventsHistory": {"type": "object", "properties": {"roundId": {"type": "string", "description": "Encripted round identifier", "example": "Rk8VenwB"}, "spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "device": {"type": "string", "description": "Player's device", "example": "web"}, "type": {"type": "string", "description": "History item type", "example": "slot"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 1.5}, "balanceBefore": {"type": "number", "description": "Balance before spin action", "example": 10}, "balanceAfter": {"type": "number", "description": "Balance after spin action", "example": 5}, "endOfRound": {"type": "boolean", "description": "Whether this spin was at the end of the round", "example": true}, "ts": {"type": "string", "description": "Time of spin (ISO 8601 timestamp)", "example": "2017-02-16T16:37:13.613Z"}, "test": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "isPayment": {"type": "boolean", "description": "If it's true, spin has transaction", "example": false}, "details": {"type": "string", "description": "Spin details", "example": "scene: main, stake: { bet: 0.01, coin: 1, lines: 100 }"}, "gameId": {"type": "string", "description": "Game code", "example": "sw_sland"}, "gameVersion": {"type": "string", "description": "Game module version", "example": "0.1.1"}, "gameCode": {"type": "string", "description": "Game code", "example": "sw_fazer6"}, "totalJpContribution": {"type": "number", "description": "Sum of all jackpot contributions in terms of the spin", "example": 10}, "totalJpWin": {"type": "number", "description": "<PERSON><PERSON> of all jackpot wins in terms of the spin", "example": 5}, "credit": {"type": "number", "description": "Change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "Change of balance not connected with game's win/bet", "example": 5}, "insertedAt": {"type": "string", "description": "ISO 8601 timestamp when spin history was uploaded to the database.", "example": "2017-02-16T16:37:13.613Z"}, "playerCode": {"type": "string", "description": "Player code", "example": "sw_sland"}, "initSettings": {"type": "string", "description": "Game init settings", "example": "{ stake: null, gameId: sw_wi0 }"}, "historyInfo": {"type": "object", "properties": {"historyRenderType": {"$ref": "#/definitions/GameHistoryRenderType"}, "historyUrl": {"type": "string", "description": "Game history URL", "example": "http://api.test/history.html"}}}}}, "TerminalTokenLock": {"type": "object", "properties": {"token": {"type": "string", "description": "Terminal token", "example": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJwbGF5ZXJVcmwiOiJodHRwczovL2FwaS5nYW1lLXpvbWUtZ2FtZS5jb206ODAxNy92MS8iLCJ0ZXJtaW5hbFVybCI6Imh0dHBzOi8vYXBpLmdhbWUtem9tZS10ZXJtaW5hbHMuY29tOjgwMDkvdjEvIiwibG9iYnlJZCI6InY1UjJuUmtFIiwiYnJhbmRJZCI6MSwib3BlcmF0b3JUeXBlIjoiZW50aXR5IiwibG9iYnlVcmwiOiJodHRwOi8vbG9jYWxob3N0OjMwMTEiLCJpYXQiOjE1OTYxMTE2OTMsImlzcyI6InNreXdpbmRncm91cCJ9.AnLChLJBLy2X_YYSqJyuh9TQ5pzWN3WkKhueHxK_diklam7L0t5jCrSpaLM9qaKWpFX6aGkhJUUHat6eNMic3Q"}}}, "UserIdsInfo": {"properties": {"id": {"type": "string", "description": "user public id", "example": "pQ3513OE"}}}, "StakeRange": {"type": "object", "properties": {"currency": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "USD"}, "coinBets": {"type": "array", "items": {"type": "number"}, "description": "Coin bets range", "example": [1, 2, 4, 8, 10]}, "lowerStakes": {"type": "array", "items": {"type": "number"}, "description": "Lower coin bets range", "example": [0.01, 0.02, 0.03]}}}, "StakeRanges": {"type": "array", "items": {"$ref": "#/definitions/StakeRange"}}, "TestReport": {"type": "object", "required": ["id", "merchCode", "merchType"], "properties": {"id": {"type": "integer", "description": "test report id", "example": 3}, "gameCode": {"type": "string", "description": "game code", "example": "sw_gol"}, "merchType": {"type": "string", "description": "merch type", "example": "mrch"}, "merchCode": {"type": "string", "description": "merch code", "example": "swftest"}, "report": {"type": "object", "properties": {"end": {"type": "object", "properties": {"end": {"type": "string", "description": "end date", "example": "2020-07-16T12:27:29.161Z"}, "start": {"type": "string", "description": "start date", "example": "2020-07-16T12:27:28.339Z"}, "tests": {"type": "number", "description": "count of tests", "example": 38}, "passes": {"type": "number", "description": "passes tests", "example": 34}, "suites": {"type": "number", "description": "suites test", "example": 8}, "pending": {"type": "number", "description": "pending tests", "example": 0}, "duration": {"type": "number", "description": "duration", "example": 822}, "failures": {"type": "number", "description": "failures tests", "example": 4}}}, "start": {"type": "object", "properties": {"total": {"type": "number", "description": "total start tests", "example": 38}}}, "tests": {"type": "array", "description": "array of tests", "items": {"$ref": "#/definitions/TestResult"}}}}}}, "TestStatus": {"type": "string", "enum": ["pass", "fail"], "description": "Status of test", "example": "pass"}, "TestResult": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of test", "example": "Get Ticket"}, "status": {"$ref": "#/definitions/TestStatus"}, "results": {"type": "array", "items": {"$ref": "#/definitions/TestResultItem"}}}}, "TestResultItem": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of test", "example": "Gets player ticket."}, "logs": {"type": "array", "items": {"$ref": "#/definitions/LogData"}}, "duration": {"type": "number", "description": "duration", "example": 822}, "currentRetry": {"type": "number", "description": "current retry", "example": 0}}}, "LogData": {"type": "object", "properties": {"time": {"type": "string", "description": "Request time", "example": "2020-09-10T14:37:12.185Z"}, "request": {"$ref": "#/definitions/LogRequestData"}, "responseCode": {"type": "number", "description": "Response code", "example": 200}, "responseBody": {"type": "object", "description": "Response body. Can be the string", "example": {"error_code": 0, "balance": 384.45, "trx_id": "SW-BET-eHLt8202vP0AAALoeHLt5eNnCD4="}}, "requestTime": {"type": "number", "description": "Request time in milliseconds", "example": 0.089}}}, "LogRequestData": {"type": "object", "properties": {"baseUrl": {"type": "string", "description": "Base request url", "example": "http://localhost:8000"}, "url": {"type": "string", "description": "Path of request url", "example": "/api/debit"}, "data": {"type": "object", "description": "Request body", "example": {"amount": 5, "event_type": "bet", "merch_id": "swesl", "merch_pwd": "*****"}}}}, "TestRunInfo": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "description": "test report id", "example": 3}}}, "TestInfo": {"type": "object", "required": ["code", "type"], "properties": {"code": {"type": "string", "description": "Merchant code, parameter is required.", "example": "swftest"}, "type": {"type": "string", "description": "Type of merchant, supported types are: ipm, mrch, mrch_json, seamless. Parameter is required.", "example": "mrch"}, "pass": {"type": "string", "description": "Merchant password, optional.", "example": "qwerty123"}, "url": {"type": "string", "description": "Url of merchant, optional.", "example": "http://localhost:8000"}, "custId": {"type": "string", "description": "Player code, which will be used in tests. If parameter is not present, random merchant test player will be used for tests.", "example": "PL0001"}, "currencyCode": {"type": "string", "description": "Player currency code. Parameter is optional. If custId is not present currencyCode of random player will be used, else CNY is default value", "example": "USD"}, "ticket": {"type": "string", "description": "Merchant external ticket, optional.", "example": "a111goodbad-1e36-4057-ad50-e488c58aadc8"}, "gameCode": {"type": "string", "description": "Game code. If parameter is not present, random merchant game will be used for tests.", "example": "sw_bb"}, "secondGameCode": {"type": "string", "description": "Game code to test play simultaneously. If parameter is not present, random merchant game will be used for tests.", "example": "sw_bb"}, "specialCases": {"type": "object", "properties": {"isNeedJPTests": {"type": "boolean", "description": "false if you don't want run JP tests", "example": true}, "isNeedFreeBetTests": {"type": "boolean", "description": "false if you don't want run free bet tests", "example": true}, "isNeedBonusAPITests": {"type": "boolean", "description": "false if you don't want run Bonus API tests", "example": true}, "isNeedMainTests": {"type": "boolean", "description": "false if you don't want run Main tests", "example": true}, "isNeedMultibetRollbackTests": {"type": "boolean", "description": "false if you don't want run Rollback tests for multibet games", "example": true}, "isNeedMultiSessionTests": {"type": "boolean", "description": "false if you don't want run Multi Session tests", "example": true}, "shouldValidateTicketOnlyOnce": {"type": "boolean", "description": "true if you want the ticket to be validated only once", "example": false}, "isNeedFinalizationTests": {"type": "boolean", "description": "true if you want run finalization tests", "example": false}}}}}, "IntegrationTestsError": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 60}, "message": {"type": "string", "description": "error message", "example": "Entity already exist"}}}, "TestHistoryReport": {"type": "object", "required": ["id", "createdAt"], "properties": {"id": {"type": "integer", "description": "test report id", "example": 3}, "createdAt": {"type": "string", "description": "creation date", "example": "2020-08-26T09:02:43.800Z"}}}, "rtpDeductionBulkUpdate": {"type": "array", "items": {"type": "object", "required": ["gameCode", "newRtpDeduction"], "properties": {"gameCode": {"type": "string", "description": "game code", "example": "sx567_code"}, "newRtpDeduction": {"type": "number", "minimum": 0, "maximum": 100, "description": "new rtp deduction", "example": 2.5}}}}, "PhantomJackpots": {"type": "object", "properties": {"jackpots": {"type": "array", "items": {"type": "object"}}}}, "ProviderGameCodeInfo": {"properties": {"id": {"type": "string", "description": "Provider game code id", "example": "pQ3513OE"}, "providerId": {"type": "string", "description": "Provider game id", "example": "pQ3513OE"}, "providerGameCode": {"type": "string", "description": "Provider game code", "example": "Title"}, "gameType": {"type": "string", "description": "Game Type (baccarat, roulette, blackjack and dragonTiger)", "example": "baccarat"}, "status": {"type": "string", "description": "status of item (active/inactive)", "enum": ["active", "inactive"], "example": "active"}, "createdAt": {"type": "string", "description": "time when a record was created (ISO 8601 timestamp)", "example": "2016-02-23T12:45:42.324Z"}, "updatedAt": {"type": "string", "description": "last time a record was updated (ISO 8601 timestamp)", "example": "2017-03-08T05:15:54.213Z"}}}, "CreateProviderGameCode": {"type": "object", "required": ["providerId", "providerGameCode", "gameType", "status"], "properties": {"providerId": {"type": "string", "description": "Provider game id", "example": "pQ3513OE"}, "providerGameCode": {"type": "string", "description": "Provider game code", "example": "Title"}, "gameType": {"type": "string", "description": "Game Type (baccarat, roulette, blackjack and dragonTiger)", "example": "baccarat"}, "status": {"type": "string", "description": "status of item (active/inactive)", "enum": ["active", "inactive"], "example": "active"}}}, "UpdateProviderGameCode": {"type": "object", "properties": {"providerId": {"type": "string", "description": "Provider game id", "example": "pQ3513OE"}, "providerGameCode": {"type": "string", "description": "Provider game code", "example": "Title"}, "gameType": {"type": "string", "description": "Game Type (baccarat, roulette, blackjack and dragonTiger)", "example": "baccarat"}, "status": {"type": "string", "description": "status of item (active/inactive)", "enum": ["active", "inactive"], "example": "active"}}}, "LimitLevel": {"type": "object", "properties": {"id": {"type": "string", "description": "Level public ID", "example": "pQ3513OE"}, "entityId": {"type": "string", "description": "Entity public ID", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Level title", "example": "low"}, "createdAt": {"type": "string", "description": "time when a record was created (ISO 8601 timestamp)", "example": "2020-11-01T12:45:42.324Z"}, "updatedAt": {"type": "string", "description": "last time a record was updated (ISO 8601 timestamp)", "example": "2020-11-02T05:15:54.213Z"}}}, "CreateLimitLevel": {"type": "object", "properties": {"title": {"type": "string", "description": "Level title", "example": "low"}}}, "EntityGameLimitLevel": {"type": "object", "properties": {"id": {"type": "string", "description": "Entity level public ID", "example": "pQ3513OE"}, "entityId": {"type": "string", "description": "Entity public ID", "example": "pQ3513OE"}, "levelId": {"type": "string", "description": "Level public ID", "example": "pQ3513OE"}, "level": {"$ref": "#/definitions/LimitLevel"}, "currency": {"type": "string", "description": "Field to disable level per currency, for default currency field can be null. if level marked as default it means currency should be null.", "example": "CNY"}, "isDefault": {"type": "boolean", "description": "Indicates level is default", "example": false}, "hidden": {"type": "boolean", "description": "Indicates level is hidden for entity", "example": false}, "inherited": {"type": "boolean", "description": "Indicates customization will be inherited to child entity", "example": false}, "gameCode": {"type": "string", "description": "Game code", "example": "sw_mrmnky"}, "createdAt": {"type": "string", "description": "time when a record was created (ISO 8601 timestamp)", "example": "2020-11-01T12:45:42.324Z"}, "updatedAt": {"type": "string", "description": "last time a record was updated (ISO 8601 timestamp)", "example": "2020-11-02T05:15:54.213Z"}}}, "CreateEntityGameLimitLevel": {"type": "object", "properties": {"levelId": {"type": "string", "description": "Level public ID", "example": "pQ3513OE"}, "gameCode": {"type": "string", "description": "Game code", "example": "sw_mrmnky"}, "currency": {"type": "string", "description": " Field to disable level per currency, for default currency field can be null. if level marked as default it means currency should be null.", "example": "CNY"}, "isDefault": {"type": "boolean", "description": "Indicates level is default", "example": false}, "hidden": {"type": "boolean", "description": "Indicates level is hidden for entity", "example": false}, "inherited": {"type": "boolean", "description": "Indicates customization will be inherited to child entity", "example": false}}}, "UpsertPlayerInfoData": {"type": "object", "required": ["playerCode"], "properties": {"playerCode": {"type": "string", "description": "player code", "example": "PL0001"}, "nickname": {"type": "string", "description": "player nickname", "example": "Nickname"}, "isVip": {"type": "boolean", "description": "VIP player or not", "example": false}, "isTracked": {"type": "boolean", "description": "flag which indicates if this is a tracked playe", "example": false}, "isPublicChatBlock": {"type": "boolean", "description": "flag which indicates if this player should be blocked for sending chat messages.", "example": false}, "isPrivateChatBlock": {"type": "boolean", "description": "flag which indicates if this player should be blocked for sending chat messages.", "example": false}, "hasWarn": {"type": "boolean", "description": "flag which indicates that player has warn", "example": false}, "restrictedIpCountries": {"$ref": "#/definitions/RestrictedIpCountries"}}}, "UpsertPlayerInfoDataStudioUser": {"type": "object", "required": ["playerCode", "brandId"], "properties": {"playerCode": {"type": "string", "description": "player code", "example": "PL0001"}, "brandId": {"type": "number", "description": "brand id", "example": 123}, "nickname": {"type": "string", "description": "player nickname", "example": "Nickname"}, "isVip": {"type": "boolean", "description": "VIP player or not", "example": false}, "isTracked": {"type": "boolean", "description": "flag which indicates if this is a tracked playe", "example": false}, "isPublicChatBlock": {"type": "boolean", "description": "flag which indicates if this player should be blocked for sending chat messages.", "example": false}, "isPrivateChatBlock": {"type": "boolean", "description": "flag which indicates if this player should be blocked for sending chat messages.", "example": false}, "hasWarn": {"type": "boolean", "description": "flag which indicates that player has warn", "example": false}}}, "GitbookLoginData": {"type": "object", "required": ["url"], "properties": {"url": {"type": "string", "description": "Gitbook login url", "example": "configData.url?jwt_token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE2MTMxNTM1NDYsImV4cCI6MTYxMzE1NzE0NiwiaXNzIjoic2t5d2luZGdyb3VwIn0.Eq5yGWcfAz7nXQTbqyWkD4P2DsZ7Xr-rNz1swSEDXCKVl-ltKU2T4ovC2_Wjybj6M6CW5SmPGRLQkAsQeJjgGg"}}}, "FlatReportInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of report", "example": 1}, "entityId": {"type": "string", "description": "Public identifier of requested entity", "example": "Hh89Kw3E"}, "report": {"type": "object", "description": "report data", "example": {"limits": {}, "limitsCustomizations": ["GAME_GROUP"]}}, "reportType": {"type": "string", "description": "type of report", "example": "ols"}, "gameCode": {"type": "string", "description": "gameCode of report", "example": "sw_al"}, "currency": {"type": "string", "description": "currency of report", "example": "USD"}, "createdAt": {"type": "string", "description": "The time when a report is posted (ISO 8601 timestamp)", "example": "2018-02-28T11:37:12.754Z"}, "updatedAt": {"type": "string", "description": "The time when a report is patched (ISO 8601 timestamp)", "example": "2018-02-28T11:37:12.754Z"}}}, "BrandFinalizationType": {"type": "string", "enum": ["notSupported", "roundStatistics", "offlinePayments", "forceFinish", "manualPayments"], "example": "forceFinish", "description": "notSupported - game server will not call finalization for this game,\nroundStatistics - game server finalizes game context using game module, game provider saves each virtual payment in wallet, but without real payments. On finalize transaction game provider sends round statistics to merchant,\nofflinePayments - in this case game server is playing game like player with payments and history,\nforceFinish - game server finalizes game context without payments and history, game provider saves finalize operation in transaction log,\nmanualPayments - game server finalizes game context using game module, game provider saves each virtual payment in wallet, but without real payments. On finalize transaction game provider game provider saves round statistics to db table to report it offline"}, "LimitFeaturesToMaxTotalStake": {"type": "boolean", "description": "True if need to limit max total stake. We will use that flag when try to apply game group limit filters. If true then we will limit max total bet to value from filters else we will apply filter but max total bet will be from game limits (or game group limits).", "example": false}, "MustWinJackpotBundled": {"type": "boolean", "description": "True if need to include critical files for Must Win Jackpot feature", "example": false}, "InstantJpEnabledForItg": {"type": "boolean", "description": "True if need to enable Instant JP feature for ITG games and related to this feature not deferred contributions", "example": false}, "UserTypeInfo": {"type": "object", "properties": {"userType": {"type": "string", "description": "user type (bo/operator_api)", "enum": ["bo", "operator_api"], "example": "bo"}}}, "GameLimitsCurrency": {"type": "object", "properties": {"currency": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "AOA"}, "toEURMultiplier": {"type": "number", "description": "Currency multiplier to EUR", "example": 500}, "copyLimitsFrom": {"type": "string", "description": "Currency from which limits can be copied", "example": "CLP"}, "version": {"type": "number", "description": "Version of game limits currency parameters set", "example": 1}}}, "GameLimitsCurrencies": {"type": "array", "items": {"$ref": "#/definitions/GameLimitsCurrency"}}, "RestrictedCountriesSolution": {"type": "object", "required": ["useCountriesFromJurisdiction"], "properties": {"useCountriesFromJurisdiction": {"type": "boolean", "description": "True if entity should use countries from jurisdiction", "example": false}}}, "JpConfigReport": {"type": "object", "required": ["gameCode"], "properties": {"gameCode": {"type": "string", "description": "Game code for which jackpot is configure", "example": "sw_al"}, "jpType": {"type": "string", "description": "Jackpot type assigned to the game", "example": "sw-fire-festival"}, "jpId": {"type": "string", "description": "Jackpot instance id assigned to the game", "example": "Entity title"}, "configuredOn": {"type": "string", "description": "The path to the entity where this JP is configured", "example": "TLE1:ENT1"}, "isInherited": {"type": "boolean", "description": "Is the instance configured in the current entity level or is it configured in some top entity level and being inherited", "example": false}, "businessEntityConfigurationLevel": {"type": "string", "enum": ["global", "shared", "brand"], "description": "Global (shared between regions),  shared (in case the instance is configured in some reseller/operator level and shared between several operators or brands) or brand ( in case the instance is configured locally for a specific brand)", "example": "shared"}, "billingConfigurationLevel": {"type": "string", "enum": ["network", "local"], "description": "Network(in case the instance is shared between different billing entities and we need to collect contributions ), local (in case the instance is locally for this business entity and we don't need to collect contributions)", "example": "network"}}}, "RTPInfo": {"type": "object", "properties": {"rtpTheoretical": {"type": "number", "example": 0}, "rtpDeduction": {"type": "number", "example": 0}, "rtpFinal": {"type": "number", "example": 0}}}, "JackpotInstance": {"type": "object", "properties": {"id": {"type": "string", "description": "Jackpot Id", "example": "jackpot_1"}, "type": {"type": "string", "description": "Jackpot type", "example": "small"}, "jpGameId": {"type": "string", "readOnly": true, "description": "Jackpot game Id", "example": "jp_game_1"}, "definition": {"description": "Definition from jackpot type", "$ref": "#/definitions/JPInfo"}, "regionCode": {"type": "string", "description": "Jackpot region", "example": "eu"}, "isTest": {"type": "boolean", "description": "Flag that determines whether the jackpot is test", "example": true}, "isGlobal": {"type": "boolean", "description": "Flag that indicates whether the jackpot is global", "example": true}, "isOwned": {"type": "boolean", "description": "Flag that indicates whether the jackpot is owned", "example": true}, "isLocal": {"type": "boolean", "description": "Flag that indicates whether the jackpot is owned by just one operator", "example": true}, "jackpotConfigurationLevel": {"type": "integer", "description": "Jackpot configuration level", "enum": [1, 2, 3, 4, 5, 6], "example": 1}, "entityId": {"type": "integer", "description": "The entity id for which the jackpot configuration level applies", "example": 1119}, "jurisdictionCode": {"type": "string", "description": "The jurisdiction code where the jackpot configuration level applies", "example": "UK"}}}, "JPInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "SW jackpot ID", "example": "sw_jackpot_id"}, "name": {"type": "string", "description": "SW JP name", "example": "sw_jackpot_name"}, "currency": {"type": "string", "description": "JP Base Currency", "example": "EUR"}, "type": {"type": "string", "description": "The type of the Jackpot.", "example": "MWJP"}, "startDate": {"type": "string", "description": "Instance creation date", "example": "2022-02-23T12:45:42.324Z"}, "status": {"type": "number", "description": "1 - for active, 0 - for closed one (will expose for several date), in most cases 1 to be returned", "example": 1}, "endDate": {"type": "string", "description": "The date and time the Jackpot was ended", "example": "2022-02-23T12:45:42.324Z"}, "externalId": {"type": "string", "description": "External SW jackpot ID. Updated after each win", "example": "sw_jackpot_id_hash"}, "externalStartDate": {"type": "string", "description": "Instance start date. Updated after each win", "example": "2023-09-07T16:45:42.324Z"}, "poolsCount": {"type": "number", "description": "Count of pools", "example": 3}, "jackpotPools": {"type": "array", "items": {"type": "object", "properties": {"poolId": {"type": "string", "description": "Pool name", "example": "small"}, "contributionPercent": {"type": "number", "description": "The contribution % of the pool", "example": 0.2}, "initialSeed": {"type": "number", "description": "The starting amount of the Jackpot (initial seed). Value in major currency units.", "example": 10}, "timeLimit": {"type": "string", "description": "The max time limit, a Jackpot can be given to a Player. N/A for normal JP", "example": 10}, "tickerAmount": {"type": "number", "description": "The total amount that has been accumulated for the Jackpot", "example": 1000}}}}, "jackpotTransfers": {"type": "array", "items": {"type": "object", "properties": {"transferAmount": {"type": "number", "description": "The amount that was transferred from a pool of a Jackpot ID, to another pool of the same or another Jackpot ID", "example": 100}, "oldPoolId": {"type": "string", "description": "The old pool ID, that the amount was transferred from", "example": "small"}, "newJackpotId": {"type": "string", "description": "The new Jackpot ID that the amount was transferred to", "example": 100}, "newPoolId": {"type": "string", "description": "The new identifier of the pool of a Jackpot ID, that the amount was transferred to", "example": "medium"}, "transferDate": {"type": "string", "description": "The date and time an amount related to Jack<PERSON> was transferred", "example": "2022-02-23T12:45:42.324Z"}}}}}}, "RestrictedIpCountries": {"type": "object", "description": "Restrictions to launch game for country detected by player IP", "properties": {"ignore": {"type": "boolean", "description": "Turn off parent restrictions", "example": false}, "countries": {"type": "array", "items": {"type": "string", "description": "Country code"}, "description": "List of restricted countries", "example": ["RU", "BY"]}}, "example": {"countries": ["RU", "BY"]}}, "ChangeNicknameData": {"type": "object", "required": ["code", "nickname"], "properties": {"code": {"type": "string", "description": "player code", "example": "PL0001"}, "nickname": {"type": "string", "description": "player nickname", "example": "Name"}}}, "LobbyDomainData": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "DNS name, start with dot or dash", "example": ".sw-lobby.com"}, "description": {"type": "string", "description": "Optional descriptive information about the domain", "example": "Cloudfront static domain for Europe"}, "provider": {"type": "string", "description": "Optional service or content provider identifier", "example": "skywind"}, "status": {"type": "string", "enum": ["active", "suspended"], "description": "Domain status", "example": "active", "default": "active"}, "expiryDate": {"type": "string", "description": "Optional expiration date for the domain (ISO 8601 timestamp)", "example": "2025-12-31T23:59:59.999Z"}, "isActive": {"type": "boolean", "description": "Flag that indicates whether the domain is considered active or inactive", "example": true}}}, "LobbyDomain": {"type": "object", "properties": {"id": {"type": "string", "description": "Domain id", "example": "Hh89Kw3E"}, "name": {"type": "string", "description": "DNS name", "example": "gc.skywindgroup.com"}, "description": {"type": "string", "description": "Optional descriptive information about the domain", "example": "Cloudfront static domain for Europe"}, "provider": {"type": "string", "description": "Optional service or content provider identifier", "example": "skywind"}, "status": {"type": "string", "enum": ["active", "suspended"], "description": "Domain status", "example": "active"}, "expiryDate": {"type": "string", "description": "Optional expiration date for the domain (ISO 8601 timestamp)", "example": "2025-12-31T23:59:59.999Z"}, "isActive": {"type": "boolean", "description": "Flag that indicates whether the domain is considered active or inactive", "example": true}, "createdAt": {"type": "string", "description": "Creation time (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "Update time (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}}}}