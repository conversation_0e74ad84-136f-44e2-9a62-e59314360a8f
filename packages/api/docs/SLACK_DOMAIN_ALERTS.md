# Slack Domain Alerts

This document describes the Slack notification system for domain blocking alerts implemented in the SW Management API.

## Overview

The system monitors domain status and sends alerts to a Slack channel when:
1. A domain is blocked by the monitoring system
2. A domain pool becomes empty (no active, unblocked domains)
3. A default domain assigned to an entity is blocked

## Features

### Alert Types

#### 1. Blocked Domain Alert
Sent when any domain is blocked by the domain monitoring system.

**Triggers:**
- Domain status changes to "BLOCKED" in domain monitoring job
- Applies to domains in pools and standalone domains

**Message includes:**
- Domain URL
- Time of blocking
- Entity information (if applicable)
- Pool information (if domain is in a pool)
- Reason for blocking

#### 2. Empty Pool Alert
Sent when a domain pool has no active, unblocked domains available.

**Triggers:**
- Checked during domain monitoring job execution
- Pool has no domains with `isActive: true` and `blockedDate: null`

**Message includes:**
- Pool name and ID
- Pool type (static or dynamic)
- Entity information using the pool

#### 3. Default Domain Blocked Alert
Sent when a domain that is set as a default domain for an entity is blocked.

**Triggers:**
- Domain blocking affects entity's default domains:
  - `staticDomainId`
  - `dynamicDomainId`
  - `lobbyDomainId`
  - `liveStreamingDomainId`
  - `ehubDomainId`

**Message includes:**
- Domain URL
- Domain type (static, dynamic, lobby, liveStreaming, ehub)
- Time of blocking
- Entity information
- Impact description

## Configuration

### Environment Variables

```bash
# Enable/disable Slack notifications
SLACK_NOTIFICATIONS_ENABLED=true

# Slack webhook URL for blocked domains channel
SLACK_BLOCKED_DOMAINS_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# HTTP timeout for Slack requests (optional, default: 5000ms)
SLACK_NOTIFICATION_TIMEOUT=5000

# Retry configuration (optional)
SLACK_RETRY_SLEEP=1000
SLACK_RETRY_MAX_TIMEOUT=10000
```

### Slack Webhook Setup

1. Go to your Slack workspace
2. Create a new app or use an existing one
3. Enable Incoming Webhooks
4. Create a webhook for the `#blocked_domains` channel
5. Copy the webhook URL to `SLACK_BLOCKED_DOMAINS_WEBHOOK_URL`

## Implementation Details

### Services

#### SlackNotificationService
- Handles HTTP communication with Slack
- Formats messages for different alert types
- Manages configuration and error handling

#### DomainAlertService
- Contains business logic for detecting alert conditions
- Queries database for affected entities
- Coordinates with SlackNotificationService

### Jobs

#### Domain Monitoring Job
- Enhanced to include empty pool checking
- Runs on the existing domain monitoring schedule
- Checks for empty pools after updating domain statuses

### Integration Points

#### Domain Watcher
The existing domain monitoring system has been enhanced to trigger alerts:
- `DomainWatcher.update()` now calls `DomainAlertService` when domains are blocked
- Alerts are sent for both pool and standalone domains

## Message Examples

### Blocked Domain Alert
```
🚫 **Domain Blocked Alert**
**Domain:** example.com
**Blocked At:** 2023-12-07T14:30:00.000Z
**Entity:** Casino ABC (ID: 123)
**Pool:** Main Static Pool (ID: 456)
**Reason:** Domain blocked by monitoring adapter
```

### Empty Pool Alert
```
⚠️ **Empty Domain Pool Alert**
**Pool:** Backup Pool (ID: 789)
**Pool Type:** static
**Status:** No active, unblocked domains available
**Entity:** Casino XYZ (ID: 456)
```

### Default Domain Blocked Alert
```
🔴 **Default Domain Blocked Alert**
**Domain:** main.casino.com
**Domain Type:** static
**Blocked At:** 2023-12-07T14:30:00.000Z
**Entity:** Casino ABC (ID: 123)
**Impact:** Entity's default domain is no longer accessible
```

## Testing

### Unit Tests
- `packages/api/src/test/services/slackNotification.spec.ts`
- `packages/api/src/test/services/domainAlert.spec.ts`

### Running Tests
```bash
npm test -- --grep "SlackNotificationService"
npm test -- --grep "DomainAlertService"
```

### Manual Testing
1. Set up test Slack webhook
2. Enable notifications in configuration
3. Trigger domain blocking through monitoring system
4. Verify alerts are received in Slack channel

## Monitoring and Troubleshooting

### Logs
All Slack notification activities are logged with the following loggers:
- `slack-notification`: HTTP requests and responses
- `domain-alert`: Alert detection and processing
- `domain-pool-alert-job`: Periodic job execution

### Common Issues

#### No alerts received
1. Check `SLACK_NOTIFICATIONS_ENABLED=true`
2. Verify webhook URL is correct
3. Check logs for HTTP errors
4. Ensure entities have `status: NORMAL`

#### Duplicate alerts
- Alerts are sent per entity using the affected domain/pool
- Multiple entities using the same pool will receive separate alerts

#### Missing pool alerts
- Ensure domain monitoring job is enabled and running
- Check domain monitoring job schedule configuration
- Verify pools have entities assigned

## Security Considerations

- Webhook URLs should be kept secure
- Consider using environment-specific channels for different deployments
- Monitor Slack API rate limits for high-volume environments

## Future Enhancements

Potential improvements:
- Alert deduplication within time windows
- Different channels for different alert types
- Alert severity levels
- Integration with incident management systems
- Metrics and dashboards for alert frequency
