
// tslint:disable:max-line-length
import { logging } from "@skywind-group/sw-utils";
import { splitEnvParameters } from "./utils/envParse";

let serverName: string = process.env.SERVER_NAME;

function parseReplicaConfigurations() {
    const replicas = [];
    let replicaIndex = 1;
    while (process.env[`PGDATABASE_REPLICA_${replicaIndex}`]) {
        replicas.push({
            database: process.env[`PGDATABASE_REPLICA_${replicaIndex}`],
            username: process.env[`PGUSER_REPLICA_${replicaIndex}`],
            password: process.env[`PGPASSWORD_REPLICA_${replicaIndex}`],
            host: process.env[`PGHOST_REPLICA_${replicaIndex}`],
            port: +process.env[`PGPORT_REPLICA_${replicaIndex}`] || 5432
        });
        replicaIndex++;
    }

    return replicas;
}

function logQueries(sql: string, timing?: number) {
    logging.logger("sequelize").info(sql);
}

const config = {

    isProduction: (): boolean => {
        return process.env.IS_PRODUCTION === "true";
    },

    getServerName: (): string => {
        return serverName || "BO";
    },

    region: process.env.REGION_TYPE || "default",

    server: {
        timeout: +process.env.SERVER_TIMEOUT || 120000,
        setName: (name: string) => {
            serverName = name;
        },
        getName: (): string => {
            return serverName;
        }
    },

    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4004,
        api: {
            isEnabled: process.env.INTERNAL_API === "true",
        },
    },

    walletConductor: {
        type: process.env.WALLET_CONDUCTOR_TYPE || "direct",
        baseURL: process.env.WALLET_HTTP_CONDUCTOR_URL || "http://127.0.0.1:6000/"
    },

    newrelic: {
        envName: process.env.NEWRELIC_ENV_NAME || "development",
        key: process.env.NEWRELIC_KEY || "395235de00803fcf5e69a2806cf655b7138918c3",
    },

    db: {
        database: process.env.PGDATABASE || "management",
        user: process.env.PGUSER,
        password: process.env.PGPASSWORD,
        host: process.env.PGHOST || "db",
        port: +process.env.PGPORT || 5432,
        ssl: {
            isEnabled: process.env.PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.PG_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS || 30000,
        schema: process.env.PGSCHEMA || "public",
        syncOnStart: process.env.SYNC_ON_START === "true"
    },

    walletArchiveDb: {
        database: process.env.MAPI_ARCHIVE_PGDATABASE,
        user: process.env.MAPI_ARCHIVE_PGUSER,
        password: process.env.MAPI_ARCHIVE_PGPASSWORD,
        host: process.env.MAPI_ARCHIVE_PGHOST,
        port: +process.env.MAPI_ARCHIVE_PGPORT,
        ssl: {
            isEnabled: process.env.MAPI_ARCHIVE_PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.MAPI_ARCHIVE_PG_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.MAPI_ARCHIVE_PG_MAX_IDLE_TIME_MS || 30000,
        schema: process.env.MAPI_ARCHIVE_PGSCHEMA || "public",
    },

    dbForReportingSlave: {
        database: process.env.PGDATABASE_SLAVE || process.env.PGDATABASE || "management",
        user: process.env.PGUSER_SLAVE || process.env.PGUSER,
        password: process.env.PGPASSWORD_SLAVE || process.env.PGPASSWORD,
        host: process.env.PGHOST_SLAVE || process.env.PGHOST || "db",
        port: +process.env.PGPORT_SLAVE || +process.env.PGPORT || 5432,
        ssl: {
            isEnabled: process.env.PG_SECURE_CONNECTION_SLAVE === "true",
            ca: process.env.PG_CA_CERT_SLAVE || "./ca.pem",
        },
        maxConnections: +process.env.PG_MAX_CONNECTIONS_SLAVE || 2,
        maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS_SLAVE || 30000,
        schema: process.env.PGSCHEMA_SLAVE || "public",
        syncOnStart: process.env.SYNC_ON_START === "true",
        maxQueryTimeout: +process.env.MAX_PG_QUERY_TIMEOUT || 45000
    },

    dbReplicas: parseReplicaConfigurations(),

    dbReportingJpSlave: {
        database: process.env.PGDATABASE_JP_SLAVE || process.env.PGDATABASE || "management",
        user: process.env.PGUSER_JP_SLAVE || process.env.PGUSER,
        password: process.env.PGPASSWORD_JP_SLAVE || process.env.PGPASSWORD,
        host: process.env.PGHOST_JP_SLAVE || process.env.PGHOST || "db",
        port: +process.env.PGPORT_JP_SLAVE || +process.env.PGPORT || 5432,
        ssl: {
            isEnabled: process.env.PG_SECURE_CONNECTION_JP_SLAVE === "true"
                || process.env.PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT_JP_SLAVE || "./ca.pem",
        },
        maxConnections: +process.env.PG_MAX_CONNECTIONS_JP_SLAVE || 2,
        maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS_JP_SLAVE || 30000,
        schema: process.env.PGSCHEMA_JP_SLAVE || "public",
        syncOnStart: process.env.SYNC_ON_START === "true"
    },

    gameServerDB: {
        database: process.env.GS_PGDATABASE || process.env.PGDATABASE || "postgres",
        user: process.env.GS_PGUSER || process.env.PGUSER,
        password: process.env.GS_PGPASSWORD || process.env.PGPASSWORD,
        host: process.env.GS_PGHOST || process.env.PGHOST || "db",
        port: +process.env.GS_PGPORT || +process.env.PGPORT || 5432,
        ssl: {
            isEnabled: process.env.GS_CONNECTION === "true",
            ca: process.env.GS_PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.GS_PG_MAX_CONNECTIONS || 2,
        maxIdleTime: +process.env.GS_PG_MAX_IDLE_TIME_MS || 30000,
        schema: process.env.GS_PGSCHEMA || "public",
    },

    gameHistory: {
        newRounds: process.env.GAME_HISTORY_NEW_ROUNDS === "true",
        redshift: {
            database: process.env.REDSHIFT_DATABASE,
            user: process.env.REDSHIFT_USER,
            password: process.env.REDSHIFT_PASSWORD,
            host: process.env.REDSHIFT_HOST || "db",
            port: +process.env.REDSHIFT_PORT || 5432,
            ssl: {
                isEnabled: process.env.REDSHIFT_SECURE_CONNECTION === "true",
            },
            maxConnections: +process.env.REDSHIFT_MAX_CONNECTIONS || 10,
            maxIdleTime: +process.env.REDSHIFT_MAX_IDLE_TIME_MS || 30000,
        },
        gameServerPort: +process.env.GAME_SERVER_PORT || 4000,
        gameServerSchema: process.env.GAME_SERVER_SCHEMA || "https",
        useStoredProcedures: (process.env.USE_PROCEDURES_FOR_HISTORY === "true") || false
    },

    redis: {
        host: process.env.MANAGEMENT_REDIS_HOST || process.env.REDIS_HOST || "redis",
        port: +process.env.MANAGEMENT_REDIS_PORT || +process.env.REDIS_PORT || 6379,
        password: process.env.MANAGEMENT_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        connectionTimeout: +process.env.MANAGEMENT_REDIS_CONNECTION_TIMEOUT || +process.env.REDIS_CONNECTION_TIMEOUT || 5000,
        sentinels: JSON.parse(process.env.MANAGEMENT_REDIS_SENTINELS || process.env.REDIS_SENTINELS || null),
        sentinelUsername: process.env.MANAGEMENT_REDIS_SENTINEL_USERNAME || process.env.REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.MANAGEMENT_REDIS_SENTINEL_PASSWORD || process.env.REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.MANAGEMENT_REDIS_CLUSTER_NAME || process.env.REDIS_CLUSTER_NAME || "redis-ha",
        minConnections: +process.env.MANAGEMENT_REDIS_MIN_CONNECTIONS || +process.env.REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.MANAGEMENT_REDIS_MAX_CONNECTIONS || +process.env.REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.MANAGEMENT_REDIS_MAX_IDLE_TIME_MS || +process.env.REDIS_MAX_IDLE_TIME_MS || 30000,
        replicationFactor: +(process.env.MANAGEMENT_REDIS_REPLICATION_FACTOR ?? process.env.REDIS_REPLICATION_FACTOR ?? 0),
        replicationTimeout: +process.env.MANAGEMENT_REDIS_REPLICATION_TIMEOUT || +process.env.REDIS_REPLICATION_TIMEOUT || 100,
        maxRetriesPerRequest: +process.env.MANAGEMENT_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        showFriendlyErrorStack: process.env.MANAGEMENT_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" || process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
    },

    walletRedis: {
        host: process.env.WALLET_REDIS_HOST || process.env.REDIS_HOST || "redis",
        port: +process.env.WALLET_REDIS_PORT || +process.env.REDIS_PORT || 6379,
        password: process.env.WALLET_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        connectionTimeout: +process.env.WALLET_REDIS_CONNECTION_TIMEOUT || +process.env.REDIS_CONNECTION_TIMEOUT || 5000,
        sentinels: JSON.parse(process.env.WALLET_REDIS_SENTINELS || process.env.REDIS_SENTINELS || null),
        sentinelUsername: process.env.WALLET_REDIS_SENTINEL_USERNAME || process.env.REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.WALLET_REDIS_SENTINEL_PASSWORD || process.env.REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.WALLET_REDIS_CLUSTER_NAME || process.env.REDIS_CLUSTER_NAME || "redis-ha",
        minConnections: +process.env.WALLET_REDIS_MIN_CONNECTIONS || +process.env.REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.WALLET_REDIS_MAX_CONNECTIONS || +process.env.REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.WALLET_REDIS_MAX_IDLE_TIME_MS || +process.env.REDIS_MAX_IDLE_TIME_MS || 30000,
        replicationFactor: +(process.env.WALLET_REDIS_REPLICATION_FACTOR ?? process.env.REDIS_REPLICATION_FACTOR ?? 0),
        replicationTimeout: +process.env.WALLET_REDIS_REPLICATION_TIMEOUT || +process.env.REDIS_REPLICATION_TIMEOUT || 100,
        maxRetriesPerRequest: +process.env.WALLET_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        showFriendlyErrorStack: process.env.WALLET_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" || process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
    },

    bonusRedis: {
        host: process.env.BONUS_REDIS_HOST || process.env.MANAGEMENT_REDIS_HOST || process.env.REDIS_HOST || "redis",
        port: +process.env.BONUS_REDIS_PORT || +process.env.MANAGEMENT_REDIS_PORT || +process.env.REDIS_PORT || 6379,
        password: process.env.BONUS_REDIS_PASSWORD || process.env.MANAGEMENT_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        connectionTimeout: +process.env.BONUS_CONNECTION_TIMEOUT || +process.env.MANAGEMENT_REDIS_CONNECTION_TIMEOUT || +process.env.REDIS_CONNECTION_TIMEOUT || 5000,
        sentinels: JSON.parse(process.env.BONUS_REDIS_SENTINELS || process.env.MANAGEMENT_REDIS_SENTINELS || process.env.REDIS_SENTINELS || null),
        sentinelUsername: process.env.BONUS_REDIS_SENTINEL_USERNAME || process.env.MANAGEMENT_REDIS_SENTINEL_USERNAME || process.env.REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.BONUS_REDIS_SENTINEL_PASSWORD || process.env.MANAGEMENT_REDIS_SENTINEL_PASSWORD || process.env.REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.BONUS_REDIS_CLUSTER_NAME || process.env.MANAGEMENT_REDIS_CLUSTER_NAME || process.env.REDIS_CLUSTER_NAME || "redis-ha",
        minConnections: +process.env.BONUS_REDIS_MIN_CONNECTIONS || +process.env.MANAGEMENT_REDIS_MIN_CONNECTIONS || +process.env.REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.BONUS_REDIS_MAX_CONNECTIONS || +process.env.MANAGEMENT_REDIS_MAX_CONNECTIONS || +process.env.REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.BONUS_REDIS_MAX_IDLE_TIME_MS || +process.env.MANAGEMENT_REDIS_MAX_IDLE_TIME_MS || +process.env.REDIS_MAX_IDLE_TIME_MS || 30000,
        replicationFactor: +(process.env.BONUS_REDIS_REPLICATION_FACTOR ?? process.env.MANAGEMENT_REDIS_REPLICATION_FACTOR ?? process.env.REDIS_REPLICATION_FACTOR ?? 0),
        replicationTimeout: +process.env.BONUS_REDIS_REPLICATION_TIMEOUT || +process.env.MANAGEMENT_REDIS_REPLICATION_TIMEOUT || +process.env.REDIS_REPLICATION_TIMEOUT || 100,
        maxRetriesPerRequest: +process.env.BONUS_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.MANAGEMENT_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        showFriendlyErrorStack: process.env.BONUS_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" || process.env.MANAGEMENT_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" || process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
    },

    playerSessionRedis: {
        host: process.env.PLAYER_SESSION_REDIS_HOST || process.env.MANAGEMENT_REDIS_HOST || process.env.REDIS_HOST || "redis",
        port: +process.env.PLAYER_SESSION_REDIS_PORT || +process.env.MANAGEMENT_REDIS_PORT || +process.env.REDIS_PORT || 6379,
        password: process.env.PLAYER_SESSION_REDIS_PASSWORD || process.env.MANAGEMENT_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
        connectionTimeout: +process.env.PLAYER_SESSION_REDIS_CONNECTION_TIMEOUT || +process.env.MANAGEMENT_REDIS_CONNECTION_TIMEOUT || +process.env.REDIS_CONNECTION_TIMEOUT || 5000,
        sentinels: JSON.parse(process.env.PLAYER_SESSION_REDIS_SENTINELS || process.env.MANAGEMENT_REDIS_SENTINELS || process.env.REDIS_SENTINELS || null),
        sentinelUsername: process.env.PLAYER_SESSION_REDIS_SENTINEL_USERNAME || process.env.MANAGEMENT_REDIS_SENTINEL_USERNAME || process.env.REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.PLAYER_SESSION_REDIS_SENTINEL_PASSWORD || process.env.MANAGEMENT_REDIS_SENTINEL_PASSWORD || process.env.REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.PLAYER_SESSION_REDIS_CLUSTER_NAME || process.env.MANAGEMENT_REDIS_CLUSTER_NAME || process.env.REDIS_CLUSTER_NAME || "redis-ha",
        minConnections: +process.env.PLAYER_SESSION_REDIS_MIN_CONNECTIONS || +process.env.MANAGEMENT_REDIS_MIN_CONNECTIONS || +process.env.REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.PLAYER_SESSION_REDIS_MAX_CONNECTIONS || +process.env.MANAGEMENT_REDIS_MAX_CONNECTIONS || +process.env.REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.PLAYER_SESSION_REDIS_MAX_IDLE_TIME_MS || +process.env.MANAGEMENT_REDIS_MAX_IDLE_TIME_MS || +process.env.REDIS_MAX_IDLE_TIME_MS || 30000,
        replicationFactor: +(process.env.PLAYER_SESSION_REDIS_REPLICATION_FACTOR ?? process.env.MANAGEMENT_REDIS_REPLICATION_FACTOR ?? process.env.REDIS_REPLICATION_FACTOR ?? 0),
        replicationTimeout: +process.env.PLAYER_SESSION_REDIS_REPLICATION_TIMEOUT || +process.env.MANAGEMENT_REDIS_REPLICATION_TIMEOUT || +process.env.REDIS_REPLICATION_TIMEOUT || 100,
        maxRetriesPerRequest: +process.env.PLAYER_SESSION_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.MANAGEMENT_REDIS_MAX_RETRIERS_PER_REQUEST || +process.env.REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        showFriendlyErrorStack: process.env.PLAYER_SESSION_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" || process.env.MANAGEMENT_REDIS_SHOW_FRIENDLY_ERROR_STACK === "true" || process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true",
    },

    security: {
        superAdminRoleId: +process.env.SUPER_ADMIN_ROLE_ID
    },

    accessToken: {
        algorithm: process.env.ACCESS_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.ACCESS_TOKEN_ISSUER || "skywindgroup",
        expiresIn: +process.env.ACCESS_TOKEN_ISSUER_EXPIRES_IN || 3600, // 1 hour
        secret: process.env.ACCESS_TOKEN_SECRET ||
            "k06u_Lq8-DfxLNacIh14tiWfOLLsyRz5v-DGw-hrarnp3tIKE1QZSdaJahH5fJgh",
        type: process.env.ACCESS_TOKEN_TYPE || "permissions"
    },
    skipAccessTokenIssuerValidation: process.env.SKIP_ACCESS_TOKEN_ISSUER_VALIDATION === "true",

    startGameToken: {
        algorithm: process.env.START_GAME_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.START_GAME_TOKEN_ISSUER || "skywindgroup",
        expiresIn: +process.env.START_GAME_TOKEN_EXPIRES_IN || 7200, // 2 hours
        secret: process.env.START_GAME_TOKEN_SECRET ||
            "yLQDmzHLqmQrjp6Z8KvuwcTwQe7TM5qMdCj4w8k8AFBXkHHDEndhUdevTw6QYPKp",
    },
    skipStartGameTokenIssuerValidation: process.env.SKIP_START_GAME_TOKEN_ISSUER_VALIDATION === "true",

    gameLauncherToken: {
        algorithm: process.env.GAME_LAUNCHER_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.GAME_LAUNCHER_TOKEN_ISSUER || "skywindgroup",
        expiresIn: +process.env.GAME_LAUNCHER_TOKEN_EXPIRES_IN || 7200, // 2 hours
        secret: process.env.GAME_LAUNCHER_TOKEN_SECRET ||
            "y35BlDMgHJEYEm4mqDver_mx4khCwk4cJaw7K_bxfG0Mzvz55w3Sl8_dCpz_VIgF",
    },

    oAuth: {
        isEnabled: process.env.OAUTH_ENABLED === "true",
        oAuthBaseUrl: process.env.OAUTH_BASE_URL || "http://localhost:9001/v1",
        clientId: process.env.OAUTH_CLIENT_ID || "95MMVLxZp3vLcRSaYD7QOL_M4rOa4tDewEE0ksW10xc",
        clientSecret: process.env.OAUTH_CLIENT_SECRET || "OT0GQZ2EcsHP6DElHUo3StINOnxo7-8gJ6hhkDN4WhEyK955Ux_G4cSyYw4qMm8228ep1U7GJQPm95nuNGzWHQ",
        ssoCookieDomain: process.env.OAUTH_SSO_COOKIE_DOMAIN || "localhost",
        ssoCookieEnv: process.env.OAUTH_SSO_COOKIE_ENV || "local",
        tokenRefreshGracePeriod: +process.env.OAUTH_TOKEN_REFRESH_GRACE_PERIOD || 24, // hours
        webAppRedirectUrl: process.env.OAUTH_WEB_APP_REDIRECT_URL || "http://locahost:3000",
        cookieSecret: process.env.OAUTH_COOKIE_SECRET || "A6JKRtGkVFr9fSPw4MllHrLjmXM3qcWTDJ1ZxXMght7O7b/k/LH9TgUOirT9kzuzlAQFW3AhHbJtJ9cUdOu+eQ==",
        redirectUri: process.env.OAUTH_REDIRECT_URI || "http://localhost:3000/v1/oauth/callback"
    },

    twoFA: {
        isEnabled: process.env.TWO_FACTOR_AUTHENTICATION === "true",
    },

    twoFAtoken: {
        algorithm: process.env.TWO_FA_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.TWO_FA_ISSUER || "skywindgroup",
        expiresIn: +process.env.TWO_FA_ISSUER_EXPIRES_IN || 120, // 2 mins
        secret: process.env.TWO_FA_SECRET ||
            "Z2mFWZcdD4scyS2BApmWzeqnkNetCQkugKQGYE3rF5cBNVDaMWekN44JAm32uVG2",
        // interval between consequent messages with code for same user
        smsEmailSendingInterval: +process.env.TWO_FA_CODE_SEND_INTERVAL_SEC || 5 // 5 secs
    },

    gameHistoryToken: {
        algorithm: process.env.GAME_HISTORY_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.GAME_HISTORY_ISSUER || "skywindgroup",
        expiresIn: +process.env.GAME_HISTORY_ISSUER_EXPIRES_IN || 3600, // 1 hour
        secret: process.env.GAME_HISTORY_SECRET ||
            "qTPWgpFcCKU8JWFMBydYmWa4TVTszDb6GZbCNC9Es3FfJXH334PCVqTCVS5WEkJ2",
    },

    replayToken: {
        algorithm: process.env.REPLAY_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.REPLAY_TOKEN_ISSUER || "skywindgroup",
        expiresIn: +process.env.REPLAY_TOKEN_ISSUER_EXPIRES_IN || 3600, // 1 hour
        secret: process.env.REPLAY_TOKEN_SECRET ||
            "zloh1RE0bmx21rGPzaJoZUYjJMqClv-bcEAo7-VMyNqfiR2DwOdnkmL6oanj5Ktr",
    },

    internalServerToken: {
        expiresIn: +process.env.INTERNAL_SERVER_TOKEN_EXPIRES_IN || 300,
        algorithm: process.env.INTERNAL_SERVER_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.INTERNAL_SERVER_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.INTERNAL_SERVER_TOKEN_SECRET || "TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
    },

    liveStudioToken: {
        expiresIn: +process.env.LIVE_STUDIO_TOKEN_EXPIRES_IN || 14400, // 4 hours
        algorithm: process.env.LIVE_STUDIO_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.LIVE_STUDIO_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.LIVE_STUDIO_TOKEN_SECRET || "Y0O9m'J>l:z]'WFKL*;Z{`oo!ES:q8"
    },

    banWordsToken: {
        expiresIn: +process.env.BAN_WORDS_TOKEN_EXPIRES_IN || 14400, // 4 hours
        algorithm: process.env.BAN_WORDS_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.BAN_WORDS_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.BAN_WORDS_TOKEN_SECRET || "Y0O9m'J>l:z]'WFKL*;Z{`oo!ES:q8"
    },

    twilio: {
        // "**********************************" - our current acc. Below are test creds
        accountSid: process.env.TWILIO_ACCOUNT_SID || "**********************************",
        // "0b52c2a396ab17d437a4d9c3fe29a6ab" - our current acc. Below are test creds
        accountToken: process.env.TWILIO_ACCOUNT_TOKEN || "7f9de274d8840db1b2edc2a3e77fa818",
        // "+***********" - our current acc. Below are test creds
        accountPhoneNumber: process.env.TWILIO_PHONE_NUMBER || "+***********",
        authSmsTemplate: process.env.TWILIO_SMS_TEMPLATE || "Your auth code is: {{authCode}}",
        defaultSmsTemplateForBO: process.env.TWILIO_DEFAULT_SMS_TEMPLATE_BO || "{{username}}, here is your sms <b>{{authCode}}</b> auth code"
    },

    mailjet: {
        publicKey: process.env.MAILJET_APIKEY_PUBLIC || "********************************",
        privateKey: process.env.MAILJET_APIKEY_PRIVATE || "********************************",
        isTest: process.env.MAILJET_IS_TEST === "true",
        sentFromEmail: process.env.MAILJET_SENT_FROM || "<EMAIL>"
    },
    defaultMailAgent: process.env.DEFAULT_MAIL_AGENT || "mailjet",

    gameToken: {
        algorithm: process.env.GAME_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.GAME_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.GAME_TOKEN_SECRET || "PjvMCu7AmUuhNTzYFqLHrYTctKxUQEpcygDJ7qePxjhWDahCQ2PqSynf93wt8ndW",
    },
    skipGameTokenIssuerValidation: process.env.SKIP_GAME_TOKEN_ISSUER_VALIDATION === "true",

    siteToken: {
        algorithm: process.env.SITE_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.SITE_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.SITE_TOKEN_SECRET || "EM8zlDKiKzyDXdzXrumeYu2uF6hSLnLQelOEslk2Chc2D8mV9u8ZIoZVmJvih0Ph",
    },

    terminalToken: {
        algorithm: process.env.TERMINAL_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.TERMINAL_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.TERMINAL_TOKEN_SECRET || "3GI2btfDs9G3nC57aim6m4O6KQo6aPijpmUGeRqV5g0ETKcYLQZAhoDkxzn5ezZ8",
    },

    resetToken: {
        outputEncoding: process.env.RESET_TOKEN_OUTPUT_ENCODING || "base64",
        expiresIn: +process.env.RESET_TOKEN_EXPIRES_IN || 900, // 15 minutes
    },

    playerLoginToken: {
        algorithm: process.env.PLAYER_LOGIN_TOKEN_ALGORITHM || "HS512",
        // we use the same as for access token
        issuer: process.env.ACCESS_TOKEN_ISSUER || "skywindgroup",
        expiresIn: +process.env.START_GAME_TOKEN_EXPIRES_IN || 7200, // 2 hours
        secret: process.env.PLAYER_LOGIN_TOKEN_SECRET || "iOWQTW53O8bYEK2YWX0D0oS1r45cZKvn",
    },
    playerLoginCheckAnotherSession: process.env.PLAYER_LOGIN_CHECK_ANOTHER_SESSION === "true",
    skipPlayerLoginTokenIssuerValidation: process.env.SKIP_PLAYER_LOGIN_TOKEN_ISSUER_VALIDATION === "true",

    playerResetPassword: {
        expiresIn: +process.env.PLAYER_RESET_PASSWORD_EXPIRES_IN || 900, // 15 minutes
        redirectTo: process.env.PLAYER_RESET_PASSWORD_REDIRECT_TO || "http://localhost:6080/",
        smtp: {
            host: "smtp.gmail.com",
            port: 465,
            auth: {
                user: "swsmanage",
                pass: "Csf-UNz-Gy2-RGX",
            },
        },
    },

    jackpotServer: {
        port: process.env.JACKPOT_SERVER_PORT || 5000,
        host: process.env.JACKPOT_SERVER_HOST || "http://localhost",
        apiPath: process.env.JACKPOT_API_PATH || "/api/v2/jpn",
        enableJPNSwagger: process.env.ENABLE_JPN_SWAGGER,
        requestTimeout: +(process.env.JPN_API_TIMEOUT || 15000),
        keepAlive: {
            maxFreeSockets: +(process.env.JPN_KEEP_ALIVE_FREE_SOCKET_COUNT || 100),
            freeSocketKeepAliveTimeout: +process.env.JPN_KEEP_ALIVE_TIMEOUT || 30000,
            socketActiveTTL: +process.env.JPN_SOCKET_ACTIVE_TTL || 60000
        },
    },

    jackpotTickerServer: {
        port: process.env.JACKPOT_TICKER_SERVER_PORT || 5001,
        host: process.env.JACKPOT_TICKER_SERVER_HOST || "http://localhost",
        apiPath: process.env.JACKPOT_TICKER_API_PATH || "/v1",
    },

    gameServer: {
        port: process.env.GAME_SERVER_PORT,
        host: process.env.GAME_SERVER_HOST,
        requestTimeout: +(process.env.GAME_SERVER_API_TIMEOUT || 15000),
        keepAlive: {
            maxFreeSockets: +(process.env.GAME_SERVER_KEEP_ALIVE_FREE_SOCKET_COUNT || 100),
            freeSocketKeepAliveTimeout: +process.env.GAME_SERVER_KEEP_ALIVE_TIMEOUT || 30000,
            socketActiveTTL: +process.env.GAME_SERVER_SOCKET_ACTIVE_TTL || 60000
        },
    },

    bi: {
        // in seconds
        tokenExpiresIn: +process.env.TABLEAU_TOKEN_EXPIRES_IN || 120,
        baseURL: process.env.TABLEAU_BASE_URL || "https://bi.gcpstg.m27613.com/#/site/cd2",
        trustServerURL: process.env.TABLEAU_TRUST_SERVER_URL || "https://bi.gcpstg.m27613.com/trusted",
        username: process.env.TABLEAU_USERNAME || "backoffice",
        showToolbar: process.env.TABLEAU_TOOLBAR === "true",
        permissionPrefixKeyentity: "keyentity:bi:report:",
        permissionPrefixEntity: "bi:report:",
        validCurrencyArray: process.env.TABLEAU_VALID_CURRENCY_ARRAY ?
            process.env.TABLEAU_VALID_CURRENCY_ARRAY.split(",") :
            ["EUR", "CNY", "USD", "KRW", "MYR"],
        domainsListLimit: process.env.BI_REPORT_DOMAINS_LIST_LIMIT || 10
    },

    settingsDefaults: {
        userIdleTimeout: +process.env.SETTINGS_DEFAULTS_USER_IDLE_TIMEOUT || 5 * 60, // 5 min
        userLoginAttempts: +process.env.SETTINGS_DEFAULTS_USER_LOGIN_ATTEMPTS || 5,
        userChangePasswordAttempts: +process.env.SETTINGS_DEFAULTS_USER_CHANGE_PASSWORD_ATTEMPTS || 3,
        userResetBlockingTimeout: +process.env.SETTINGS_DEFAULTS_USER_RESET_BLOCKING_TIMEOUT || 3600, // 1 hour
        userCaptchaMasterKey: process.env.USER_CAPTCHA_MASTER_KEY || "Qdk9,9,FaVx>T,F&+P$GVuLQR=T~J)TC",
        userLoginAttemptsShowCaptcha: +process.env.USER_LOGIN_ATTEMPTS_SHOW_CAPTCHA || 3,
        userPwdResetAttemptsShowCaptcha: +process.env.USER_PWD_RESET_ATTEMPTS_SHOW_CAPTCHA || 3,
        userCaptchaKeepAlive: +process.env.USER_CAPTCHA_KEEP_ALIVE || 3600, // 1 hour
        userPwdResetAttempts: +process.env.SETTINGS_DEFAULTS_USER_PWD_RESET_ATTEMPTS || 3,
        userCaptchaEnable: process.env.USER_CAPTCHA_ENABLE === "true"
    },

    offlineWinGameProvider: {
        defaultMaxRetryAttempts: +process.env.OF_GP_DEF_MAX_RETRY_ATTEMPTS || 8,
        defaultMinRetryTimeout: +process.env.OF_GP_DEF_MIN_RETRY_TIMEOUT || 120
    },

    playerSettingsLogin: {
        playerIdleTimeout: +process.env.PLAYER_IDLE_LOGIN_TIMEOUT || 5 * 60, // 5 min
        playerLoginAttempts: +process.env.PLAYER_LOGIN_ATTEMPTS || 5,
        playerChangePasswordAttempts: +process.env.PLAYER_CHANGE_PASSWORD_ATTEMPTS || 3,
        playerResetBlockingTimeout: +process.env.PLAYER_RESET_BLOCKING_TIMEOUT || 3600, // 1 hour
        playerCaptchaMasterKey: process.env.PLAYER_CAPTCHA_MASTER_KEY || "Qdk9,9,FaVx>T,F&+P$GVuLQR=T~J)TC",
        playerLoginAttemptsShowCaptcha: +process.env.PLAYER_LOGIN_ATTEMPTS_SHOW_CAPTCHA || 3,
        playerPwdResetAttemptsShowCaptcha: +process.env.PLAYER_PWD_RESET_ATTEMPTS_SHOW_CAPTCHA || 3,
        playerCaptchaKeepAlive: +process.env.PLAYER_CAPTCHA_KEEP_ALIVE || 3600, // 1 hour
        playerCaptchaEnable: process.env.PLAYER_CAPTCHA_ENABLE === "true"
    },

    publicId: {
        password: process.env.PCID_PASSWORD || "PjvMCu7AmUuhNTzYFqLHrYTctKxUQEpcygDJ7qePxjhWDahCQ2PqSynf93wt8ndW",
        minHashLength: +process.env.PCID_MIN_HASH_LENGTH || 8,
    },

    jobsProcessing: {
        attempts: +process.env.JOBS_PROCESSING_ATTEMPTS || 10,
        backoff: {
            delay: +process.env.JOBS_PROCESSING_BACKOFF_DELAY || 60 * 1000,
            type: process.env.JOBS_PROCESSING_BACKOFF_TYPE || "fixed",
        },
    },

    jobsMonitoringUI: {
        port: +process.env.JOBS_MONITORING_UI || 3500,
        apiURL: process.env.JOBS_MONITORING_API || "/api",
        baseURL: process.env.JOBS_MONITORING_BASE_URL || "/ui",
        updateInterval: +process.env.JOBS_MONITORING_UPDATE_INTERVAL || 100,
    },
    logLevel: process.env.LOG_LEVEL || "info",

    graylog: {
        host: process.env.GRAYLOG_HOST || undefined,
        port: +process.env.GRAYLOG_PORT || undefined,
    },

    logParams: {
        secureSalt: "**********!#$*&^%)(",
        secureKeys: [
            "password",
            "newPassword",
            "key",
            "token",
            "accessToken",
            "secret",
            "merch_pwd",
            "confirmPassword",
            "privateToken",
            "privateKey"
        ],
        skipBodyOnUrl: ["/gameprovider/v1/play/"],
        keylen: 40,
        encryptIters: 48,
    },

    // logging all POSTGRES queries to console
    queryLogging: process.env.POSTGRES_QUERY_LOGGING === "true" ? logQueries : (sql: string, timing?: number) => {
    }, // tslint:disable-line

    cache: {
        ttl: +process.env.CACHE_TTL || 60,
        checkPeriod: +process.env.CACHE_CHECK_PERIOD || 60,
    },

    gameLimitsCurrenciesCache: {
        ttl: +process.env.GAME_LIMITS_CURRENCIES_CACHE_TTL || 60
    },

    entitiesCache: {
        ttl: +process.env.ENTITIES_CACHE_TTL || 5 * 60,
        checkPeriod: +process.env.ENTITIES_CACHE_CHECK_PERIOD || 0,
        queryRetries: {
            sleep: +process.env.ENTITIES_CACHE_QUERY_RETRIES_SLEEP || 10,
            maxTimeout: +process.env.ENTITIES_CACHE_QUERY_RETRIES_MAX_TIMEOUT || 1000
        }
    },

    entityGameCache: {
        ttl: +process.env.ENTITY_GAME_CACHE_TTL || 600, // 10 min
        checkPeriod: +process.env.ENTITY_GAME_CACHE_CHECK_PERIOD || 300, // 5 min
    },

    entityJurisdictionCache: {
        ttl: +process.env.ENTITY_JURISDICTION_CACHE_TTL || 600, // 10 min
        checkPeriod: +process.env.ENTITY_JURISDICTION_CACHE_CHECK_PERIOD || 300, // 5 min
    },

    gameCodesCache: {
        ttl: +process.env.GAME_CODES_CACHE_TTL || 600, // 10 min
        checkPeriod: +process.env.GAME_CODES_CACHE_CHECK_PERIOD || 300, // 5 min
    },

    mergedEntitySettingsCache: {
        ttl: +process.env.MERGED_ENTITY_SETTINGS_CACHE_TTL || 5 * 60,
        checkPeriod: +process.env.MERGED_ENTITY_SETTINGS_CACHE_CHECK_PERIOD || 0
    },

    staticDomainCache: {
        ttl: +process.env.STATIC_DOMAIN_CACHE_TTL || 600, // 10 min
        checkPeriod: +process.env.STATIC_DOMAIN_CACHE_CHECK_PERIOD || 300, // 5 min
    },

    staticDomainPoolCache: {
        ttl: +process.env.STATIC_DOMAIN_POOL_CACHE_TTL || 600, // 10 min
        checkPeriod: +process.env.STATIC_DOMAIN_POOL_CACHE_CHECK_PERIOD || 300, // 5 min
    },

    dynamicDomainCache: {
        ttl: +process.env.DYNAMIC_DOMAIN_CACHE_TTL || 600, // 10 min
        checkPeriod: +process.env.DYNAMIC_DOMAIN_CACHE_CHECK_PERIOD || 300, // 5 min
    },

    gameVersionsCache: {
        ttl: +process.env.GAME_VERSIONS_CACHE_TTL || 3600,
        checkPeriod: +process.env.GAME_VERSIONS_CACHE_CHECK_PERIOD || 1800,
    },

    gameCategoriesCache: {
        cache: {
            ttl: +process.env.GAME_CATEGORY_CACHE_TTL || 3600, // 1 hour
            checkPeriod: +process.env.GAME_CATEGORY_CACHE_CHECK_PERIOD || 1800, // 30 min
        }
    },

    favoriteGamesCache: {
        cache: {
            ttl: +process.env.FAVORITE_GAMES_CACHE_TTL || 3600, // 1 hour
            checkPeriod: +process.env.FAVORITE_GAMES_CACHE_CHECK_PERIOD || 1800, // 30 min
        }
    },

    merchantBalance: {
        cache: {
            ttl: +process.env.MERCHANT_BALANCE_CACHE_TTL || 3600, // 1 hour
            checkPeriod: +process.env.MERCHANT_BALANCE_CACHE_CHECK_PERIOD || 1800, // 30 min
            defaultCurrencyTtl: +process.env.MERCHANT_BALANCE_DEFAULT_CURRENCY_CACHE_TTL || 90 // 1.5 min
        }
    },

    merchantTypes: {
        cache: {
            ttl: +process.env.MERCHANT_TYPE_CACHE_TTL || 3600, // 1 hour
            checkPeriod: +process.env.MERCHANT_TYPE_CACHE_CHECK_PERIOD || 1800, // 30 min
        }
    },

    availableSites: {
        cache: {
            ttl: +process.env.AVAILABLE_SITES_CACHE_TTL || 3600, // 1 hour
            checkPeriod: +process.env.AVAILABLE_SITES_CACHE_CHECK_PERIOD || 1800, // 30 min
        }
    },

    roles: {
        cache: {
            ttl: +process.env.ROLES_CACHE_TTL || 3600, // 1 hour
            checkPeriod: +process.env.ROLES_CACHE_CHECK_PERIOD || 1800, // 30 min
        }
    },

    lobbies: {
        cache: {
            ttl: +process.env.LOBBIES_CACHE_TTL || 0, // infinity
            checkPeriod: +process.env.LOBBIES_CACHE_CHECK_PERIOD || 0, // no periodic check
        },
        domainTemplate: process.env.LOBBIES_DOMAIN_TEMPLATE || "lobby-qa.ss211208.com"
    },

    auditSummary: {
        cache: {
            ttl: +process.env.AUDIT_SUMMARY_CACHE_TTL || 0, // infinity
            checkPeriod: +process.env.AUDIT_SUMMARY_CACHE_CHECK_PERIOD || 0, // no periodic check
        }
    },

    limits: {
        apiFilterMaxLimit: +process.env.API_FILTER_MAX_LIMIT || 100,
        periodLimit: +process.env.PERIOD_LIMIT || 3, // 3 month
        gameFilterMaxLimit: +process.env.GAME_FILTER_MAX_LIMIT || 1000,
        liveFilterMaxLimit: +process.env.LIVE_FILTER_MAX_LIMIT || 1000,
        auditMaxLimit: +process.env.AUDIT_MAX_LIMIT || 1000,
        isPeriodLimitEnabled: process.env.IS_PERIOD_LIMIT_ENABLED === "true"
    },

    csvExport: {
        defaultLimitLines: +process.env.CSV_EXPORT_DEFAULT_LIMIT || 1000,
        maxLimitLines: +process.env.CSV_EXPORT_MAX_LIMIT || 100000,
        periodOfNoLimitation: +process.env.CSV_EXPORT_PERIOD_NO_LIMIT || 24 * 60 * 60,
        limitWithBalance: +process.env.CSV_EXPORT_WITH_BALANCE || 500,
        maxSpinsDetails: +process.env.CSV_MAX_SPINS_DETAILS || 1000
    },

    currencyRatesHistory: {
        enabled: () => process.env.CURRENCY_RATES_HISTORY_ENABLED === "true" || config.isProduction(),
        schedule: process.env.CURRENCY_RATES_HISTORY_UPDATE_SCHEDULE || "0 2 * * *",
        retryInterval: +process.env.ORX_RETRY_INTERVAL_MS || 60000, // 1 Minute in milliseconds
    },

    paymentApi: {
        url: process.env.PAYMENT_API_URL || "http://localhost:3500",
    },

    integrationTestApi: {
        url: process.env.INTEGRATION_TEST_API_URL || "http://localhost:3700",
    },

    domainMonitoring: {
        cronJob: {
            enabled: (process.env.DOMAIN_MONITORING_JOB_ENABLED === "true"),
            schedule: process.env.DOMAIN_MONITORING_JOB_SCHEDULE || "*/5 * * * *", // every 5min
            timeout: +process.env.DOMAIN_MONITORING_JOB_TIMEOUT || 60, // seconds for lock object in redis
            runOnServerStart: (process.env.DOMAIN_MONITORING_JOB_RUN_IMMEDIATELY === "true"),
        },
        adapters: {
            tapking: {
                baseUrl: process.env.DOMAIN_MONITORING_ADAPTER_TAPKING_BASE_URL,
                token: process.env.DOMAIN_MONITORING_ADAPTER_TAPKING_TOKEN,
                regions: splitEnvParameters(process.env.DOMAIN_MONITORING_ADAPTER_TAPKING_REGIONS, []),
            },
            timeout: parseInt(process.env.DOMAIN_MONITORING_ADAPTER_TIMEOUT || "30000"),
            retryDelay: parseInt(process.env.DOMAIN_MONITORING_ADAPTER_RETRY_DELAY || "1000"),
            retryAttempts: parseInt(process.env.DOMAIN_MONITORING_ADAPTER_RETRY_ATTEMPTS || "3"),
            keepAlive: {
                maxFreeSockets: parseInt(process.env.DOMAIN_MONITORING_ADAPTER_KEEP_ALIVE_FREE_SOCKET_COUNT || "10"),
                freeSocketKeepAliveTimeout: parseInt(process.env.DOMAIN_MONITORING_ADAPTER_KEEP_ALIVE_TIMEOUT || "30000"),
                socketActiveTTL: parseInt(process.env.DOMAIN_MONITORING_ADAPTER_SOCKET_ACTIVE_TTL || "60000")
            }
        },
    },

    terminalSettings: {
        playerUrl: process.env.TERMINAL_PLAYER_URL || "https://api-player.m27613.com",
        terminalUrl: process.env.TERMINAL_URL || "https://api-terminal.m27613.com",
        lobbyUrl: process.env.TERMINAL_LOBBY_URL || "http://localhost:3011",
    },

    bodyParserJsonLimit: +process.env.BODY_PARSER_JSON_LIMIT || 5242880,
    bodyParserUrlLimit: +process.env.BODY_PARSER_URL_LIMIT || 5242880,
    compressionThreshold: +process.env.COMPRESSION_THRESHOLD || 1024,

    promo: {
        maxFreebetsToAssign: +process.env.PROMO_MAX_FREEBETS_ASSIGN || 999,
        // cron schedule to check promotion start date, e.g. "* * * * *", switched off by default
        checkPromoStartedJobSchedule: process.env.CHECK_PROMO_STARTED_SCHEDULE
    },

    userPasswordChangeCheck: {
        isEnabled: process.env.USER_PASSWORD_CHANGE_CHECK === "true",
    },

    userPasswordForceChangeCheck: {
        isEnabled: process.env.USER_PASSWORD_FORCE_CHANGE_CHECK === "true",
        daysPeriod: +process.env.USER_PASSWORD_FORCE_CHANGE_PERIOD || 90 // number of days
    },

    userSessionCheck: {
        isEnabled: process.env.ENABLE_USER_SESSION_CHECK === undefined ? true : process.env.ENABLE_USER_SESSION_CHECK === "true"
    },

    checkEntityDomain: process.env.CHECK_ENTITY_DOMAIN === "true",

    responsibleGaming: {
        lossLimitCoolingOffPeriod: +process.env.LOSS_LIMIT_COOL_OFF_HRS || 72,
        depositLimitCoolingOffPeriod: +process.env.DEPOSIT_LIMIT_COOL_OFF_HRS || 24
    },

    historyService: {
        // default value is for CD.D environment
        baseUrl: process.env.HISTORY_SERVICE_URL || "http://35.229.139.117:65432",
        token: {
            algorithm: process.env.ACCESS_TOKEN_ALGORITHM || "HS512",
            issuer: process.env.ACCESS_TOKEN_ISSUER || "skywindgroup",
            expiresIn: +process.env.ACCESS_TOKEN_ISSUER_EXPIRES_IN || 3600,
            secret:
                process.env.HISTORY_JWT_KEY || "yLQDmzHLqmQrjp6Z8KvuwcTwQe7TM5qMdCj4w8k8AFBXkHHDEndhUdevTw6QYPKp",
        },
    },

    keepAliveTimeout: +process.env.KEEP_ALIVE_TIMEOUT || 0,

    migration: {
        support: (process.env.SUPPORT_MIGRATION === "true") || false,
        forceCleanupAttempts: +(process.env.MIGRATION_FORCE_CLEANUP_ATTEMPTS || 10),
        checkStuckMigrationInternal: +(process.env.MIGRATION_CHECK_STUCK_INTERNAL || 15 * 60 * 1000),
        crossGsMigrationBatchSize: +(process.env.CROSS_GS_MIGRATION_BATCH_SIZE || 100)
    },

    liveManager: {
        baseUrl: process.env.LIVE_MANAGER_URL || "http://localhost:3010",
        port: +process.env.LIVE_MANAGER_PORT || 3010,
        schema: process.env.LIVE_MANAGER_SCHEMA || "https",
        path: process.env.LIVE_MANAGER_PATH || "",
    },
    socket: {
        v2: {
            path: process.env.SOCKET_V2_PATH || "",
        },
        v4: {
            path: process.env.SOCKET_V4_PATH || "/socket-v4",
        },
    },

    liveStudio: {
        port: +process.env.LIVE_STUDIO_PORT || 3011,
    },
    chatSettings: {
        port: +process.env.LIVE_CHAT_SETTINGS_PORT || 3012,
    },

    liveDeploymentGroupEnabled: process.env.LIVE_DEPLOYMENT_GROUP_ENABLED === "true",
    liveDeploymentGroupRoute: process.env.LIVE_DEPLOYMENT_GROUP_ROUTE || "live",

    extGameproviderHistory: {
        on: process.env.EXT_BET_WIN_HISTORY_ENABLE !== "false",
        awaitHistorySave: process.env.AWAIT_EXT_HISTORY_SAVE !== "false"
    },

    phantom: {
        enablePhantomFeatures: (process.env.PHANTOM_ENABLE === "true"),
        apiUrl: process.env.PHANTOM_API_URL || "http://jackpot-api.test.phantomental.com/features/jackpot",
        failFast: process.env.PHANTOM_API_FAIL_FAST === "true",
        secret: process.env.PHANTOM_API_SECRET || "f2031cb7e45b321f307b",

        keepAlive: {
            maxFreeSockets: +(process.env.PHANTOM_KEEP_ALIVE_FREE_SOCKET_COUNT || 5),
            freeSocketKeepAliveTimeout: +process.env.PHANTOM_KEEP_ALIVE_TIMEOUT || 30 * 1000,
            socketActiveTTL: +process.env.PHANTOM_SOCKET_ACTIVE_TTL || 60 * 1000
        },
        requestTimeout: +process.env.PHANTOM_REQUEST_TIMEOUT || 1000,
        retries: {
            sleep: +(process.env.PHANTOM_JACKPOTS_RETRIES_SLEEP || 10),
            maxTimeout: +process.env.PHANTOM_JACKPOTS_RETRIES_MAX_TIMEOUT || 1000,
        },
    },

    gameLauncherUrlTemplate: process.env.GAME_LAUNCHER_URL_TEMPLATE || "https://{dynamicDomain}/casino/launch?token={launcherToken}",
    wrapperLauncherUrlTemplate: process.env.WRAPPER_LAUNCHER_URL_TEMPLATE || "https://{staticDomain}/wrapper/{wrapperVersion}/proxy.html?external_game_url={externalGameUrl}",

    paymentStatusJob: {
        schedule: process.env.PAYMENT_STATUS_JOB_SCHEDULE,
        timeout: +process.env.PAYMENT_STATUS_JOB_TIMEOUT || 60, // seconds for lock object in redis
        batchSize: +process.env.PAYMENT_STATUS_JOB_BATCH_SIZE || 1000,
        paymentTimeout: +process.env.PAYMENT_TIMEOUT || 600000,     // 10 mins by default
    },

    reportPOPCriticalFilesJob: {
        schedule: process.env.POP_REPORT_CRITICAL_FILES_JOB_SCHEDULE || "0 */24 * * *", // every 24h
        enabled: (process.env.POP_REPORT_CRITICAL_FILES_JOB_ENABLED === "true"),
        timeout: +process.env.POP_REPORT_CRITICAL_FILES_JOB_TIMEOUT || 60, // seconds for lock object in redis
        runOnServerStart: (process.env.POP_REPORT_CRITICAL_FILES_JOB_RUN_IMMEDIATELY === "true"),
        apiPort: +process.env.POP_REPORT_CRITICAL_FILES_API_PORT || Number.NaN,
        regulations: JSON.parse(process.env.POP_REPORT_CRITICAL_FILES_JOB_REGULATIONS || "[\"italian\",\"brazilian\"]")
    },

    lowBalanceNotificationsJob: {
        schedule: process.env.LOW_BALANCE_JOB_SCHEDULE || "0 */24 * * *", // every 24h,
        timeout: +process.env.LOW_BALANCE_JOB_TIMEOUT || 60, // seconds for lock object in redis
        enabled: (process.env.LOW_BALANCE_JOB_ENABLED === "true"),
    },



    flatReports: {
        job: {
            schedule: process.env.FLAT_REPORTS_JOB_SCHEDULE || "0 */24 * * *", // every 24h
            enabled: (process.env.FLAT_REPORTS_JOB_ENABLED === "true"),
            timeout: +process.env.FLAT_REPORTS_JOB_TIMEOUT || 600, // seconds for lock object in redis
            runOnServerStart: (process.env.FLAT_REPORTS_JOB_RUN_IMMEDIATELY === "true"),
            types: JSON.parse(process.env.FLAT_REPORTS_JOB_TYPES || "[\"es\"]"),
            entityIds: JSON.parse(process.env.FLAT_REPORTS_JOB_ENTITY_IDS || "[]"),
            withChildren: (process.env.FLAT_REPORTS_JOB_WITH_CHILDREN === "true"),
        },
        notifiedJob: {
            schedule: process.env.FLAT_REPORTS_NOTIFIED_JOB_SCHEDULE || "*/5 * * * *", // every 5min
            enabled: (process.env.FLAT_REPORTS_NOTIFIED_JOB_ENABLED === "true"),
            timeout: +process.env.FLAT_REPORTS_NOTIFIED_JOB_TIMEOUT || 60, // seconds for lock object in redis
            runOnServerStart: (process.env.FLAT_REPORTS_NOTIFIED_JOB_RUN_IMMEDIATELY === "true"),
            types: JSON.parse(process.env.FLAT_REPORTS_NOTIFIED_JOB_TYPES || "[\"es\"]"),
            limit: +process.env.FLAT_REPORTS_NOTIFIED_JOB_LIMIT || 10,
        },
    },

    banWords: {
        port: +process.env.BAN_WORDS_PORT || 3012,
        baseUrl: process.env.BAN_WORDS_URL || "http://localhost:3012",
        timeout: +process.env.BAN_WORDS_TIMEOUT || 3000,

        job: {
            schedule: process.env.BAN_WORDS_JOB_SCHEDULE || "*/30 * * * *", // every 30min,
            timeout: +process.env.BAN_WORDS_JOB_TIMEOUT || 60, // seconds for lock object in redis
            enabled: (process.env.BAN_WORDS_JOB_ENABLED === "true"),
            runOnServerStart: (process.env.BAN_WORDS_JOB_RUN_IMMEDIATELY === "true")
        },

        googleBucket: {
            bucketName: process.env.BAN_WORDS_GOOGLE_BUCKET_NAME,
            credentials: {
                private_key:
                    process.env.BAN_WORDS_GOOGLE_BUCKET_PRIVATE_KEY &&
                    process.env.BAN_WORDS_GOOGLE_BUCKET_PRIVATE_KEY.replace(/\\n/gm, "\n"),
                client_email: process.env.BAN_WORDS_GOOGLE_BUCKET_CLIENT_EMAIL
            },
            projectId: process.env.BAN_WORDS_GOOGLE_BUCKET_PROJECT_ID || "gcpstg",
            refreshTime: +process.env.BAN_WORDS_REFRESH_TIME || 1800000,
            fileName: process.env.BAN_WORDS_GOOGLE_BUCKET_FILE_NAME || "ban-words.json",
        },
    },

    disableDbInitOnServerStart: process.env.DISABLE_DB_INIT_ON_SERVER_START === "true",

    deferredPayment: {
        on: process.env.DEFERRED_PAYMENT_ENABLE === "true",
        url: process.env.DEFERRED_PAYMENT_API_URL || "http://deferred-payment-api:3910",
        validateSignature: process.env.DEFERRED_PAYMENT_VALIDATE_SIGNATURE !== "false",
        keepAlive: {
            maxFreeSockets: +(process.env.DEFERRRED_PAYMENT_KEEP_ALIVE_FREE_SOCKET_COUNT || 100),
            freeSocketKeepAliveTimeout: +process.env.DEFERRRED_PAYMENT_KEEP_ALIVE_TIMEOUT || 30000,
            socketActiveTTL: +process.env.DEFERRRED_PAYMENT_SOCKET_ACTIVE_TTL || 60000
        },
        retries: {
            sleep: +(process.env.DEFERRRED_PAYMENT_RETRIES_SLEEP || 0),
            maxTimeout: +process.env.DEFERRRED_PAYMENT_RETRIES_MAX_TIMEOUT || 400,
        },
        requestTimeout: +process.env.DEFERRRED_PAYMENT_REQUEST_TIMEOUT || 200
    },

    walletExternalLog: {
        on: process.env.EXTERNAL_TRX_ON === "true",
        config: {
            kafkaBrokerHostnames: process.env.EXTERNAL_TRX_KAFKA_BROKERS || "kafka:9092",
            topicName: process.env.EXTERNAL_TRX_TOPIC_NAME || "wallet_operation_log",
            publish: {
                ackTimeoutMs: +process.env.EXTERNAL_TRX_ACK_TIMEOUT || 1000,
                clientCreationTimeout: +process.env.EXTERNAL_CLIENT_CREATION_TIMEOUT || 6000,
                requestTimeout: +process.env.EXTERNAL_TRX_REQUEST_TIMEOUT || 1000,
                maxSendAttemptTimeout: +process.env.EXTERNAL_TRX_MAX_ATTEMPT_TIMEOUT || 10000
            }
        }
    },

    defaultCountry: process.env.DEFAULT_COUNTRY || "XX",

    operatorInfoNotification: {
        channelPrefix: process.env.OPERATOR_INFO_NOTIFICATION_CHANNEL || "operator-info",
        type: process.env.OPERATOR_INFO_NOTIFICATION_TYPE || "redis"
    },

    slackNotifications: {
        enabled: process.env.SLACK_NOTIFICATIONS_ENABLED === "true",
        blockedDomainsWebhookUrl: process.env.SLACK_BLOCKED_DOMAINS_WEBHOOK_URL || "",
        timeout: +process.env.SLACK_NOTIFICATION_TIMEOUT || 5000,
        retryConfig: {
            sleep: +process.env.SLACK_RETRY_SLEEP || 1000,
            maxTimeout: +process.env.SLACK_RETRY_MAX_TIMEOUT || 10000
        },
        domainPoolAlertThreshold: +process.env.SLACK_DOMAIN_POOL_ALERT_THRESHOLD || 1
    },

    nats: {
        servers: JSON.parse(process.env.NATS_SERVERS || `["${process.env.NATS_URL || "nats://nats:4222"}"]`)
    },

    trxIdRange: process.env.WALLET_TRX_ID_MIN || process.env.WALLET_TRX_ID_MAX ? {
        min: process.env.WALLET_TRX_ID_MIN,
        max: process.env.WALLET_TRX_ID_MAX,
    } : undefined,

    loggingOutput: (process.env.LOGGING_OUTPUT_TYPE || process.env.GRAYLOG_HOST && "graylog" || "console") as any,

    authGateway: {
        routes: JSON.parse(process.env.AUTH_GATEWAY_ROUTES || "null") || {
            "srt-api": "http://localhost:5100",
            "jackpot-api": "http://localhost:5000",
        }
    },

    audit: {
        on: process.env.AUDIT_ENABLE === "true"
    },

    // TODO After a trial period consider another schema of storing master-keys in redis or in the DB
    getGitBookSettings: space => {
        const urlMap = [
            {
                space: "skywind-api-documentation",
                key: process.env.GITBOOK_API_KEY || "bd3bb00d-a9be-42ca-99d8-3d65eed876b1",
                url: process.env.GITBOOK_API_URL || "https://skywind.gitbook.io/skywind-api-documentation"
            }, {
                space: "skywind-seamless-api-documentation",
                key: process.env.GITBOOK_API_SEAMLESS_KEY || "068337bd-203b-4b71-8e43-d26ab12515a5",
                url: process.env.GITBOOK_API_SEAMLESS_URL || "https://skywind.gitbook.io/skywind-seamless-api-documentation"
            }
        ];
        const data = urlMap.find(p => p.space === space);
        return {
            config: {
                algorithm: process.env.GIT_BOOK_TOKEN_ALGORITHM || "HS512",
                issuer: process.env.GIT_BOOK_TOKEN_ISSUER || "skywindgroup",
                expiresIn: +process.env.GIT_BOOK_TOKEN_EXPIRES_IN || 3600, // 1 hour
                secret: data ? data.key : ""
            },
            url: data ? data.url : ""
        };
    },

    playerHistoryPeriod: +process.env.PLAYER_HISTOTY_PERIOD_MS || 7 * 24 * 60 * 60 * 1000, // a week by default
    denyOperationsWithPhysicalTable: process.env.DENY_OPRATIONS_WITH_PHYSICAL_TABLE === "true",

    playerNicknameChangeAttemptsLimit: +process.env.PLAYER_NICKNAME_CHANGE_ATTEMPTS_LIMIT || 3,
    defaultNumberOfMonthsForAggrWinBetReports: +process.env.DEFAULT_NUMBER_OF_MONTHS_FOR_AGGR_WIN_BET_REPORTS || 1,

    jackpotEndDateNotOlderThanDaysAmount: +process.env.JACKPOT_END_DATE_NOT_OLDER_THAN_DAYS_AMOUNT || 5,

    regulations: process.env.REGULATIONS ?
        process.env.REGULATIONS.split(",") :
        ["italian", "spanish", "greek", "lithuanian", "swiss", "brazilian"],

    merchantGameRestrictionsUseIp: process.env.MERCHANT_GAME_RESTRICTIONS_USE_IP === "true",
    restrictedIpCountries: process.env.MERCHANT_GAME_RESTRICTIONS_USE_IP_FROM_COUNTRIES
        ? process.env.MERCHANT_GAME_RESTRICTIONS_USE_IP_FROM_COUNTRIES.split(",")
        : [],

    itgLicenseeId: process.env.SW_LICENSEE_NAME_FOR_INTOUCH || "SKW0",
    itgBaseUrl: process.env.INTOUCH_BASE_URL || "https://stag-wa.slotfactory.com",
    itgGameCodePrefix: process.env.INTOUCH_GAME_CODE_PREFIX || "itg_",

    analytics: {
        on: process.env.ANALYTICS_ON === "true",
        playerBlocking: {
            on: process.env.ANALYTICS_PLAYER_BLOCKING_ON === "true",
            type: process.env.ANALYTICS_PLAYER_BLOCKING_TYPE || "player_blocking",
        },
        domains: {
            on: process.env.ANALYTICS_DOMAINS_ON === "true",
            type: process.env.ANALYTICS_DOMAINS_TYPE || "domains"
        },
        kafka: {
            topicName: process.env.ANALYTICS_TOPIC_NAME || "analytics",
            kafkaBrokerHostnames: process.env.ANALYTICS_KAFKA_BROKERS || "kafka:9092",
            ackTimeoutMs: +process.env.ANALYTICS_KAFKA_ACK_TIMEOUT || 1000,
            clientCreationTimeout: +process.env.ANALYTICS_KAFKA_CLIENT_CREATION_TIMEOUT || 6000,
            requestTimeout: +process.env.ANALYTICS_KAFKA_REQUEST_TIMEOUT || 1000,
            maxSendAttemptTimeout: +process.env.ANALYTICS_KAFKA_MAX_ATTEMPT_TIMEOUT || 10000
        }
    },

    egpConfigurations: JSON.parse(process.env.EGP_CONFIGURATIONS || "{\"PP\":{\"url\":\"http://localhost:3250/\",\"promoLocation\": \"EGP\"}}"),
    asyncLocalWalletEnabled: process.env.ASYNC_LOCAL_WALLET_ENABLED === "true",
    allowedHTTPMethods: splitEnvParameters(
        process.env.ALLOWED_HTTP_METHODS,
        ["GET", "POST", "OPTIONS", "DELETE", "PATCH", "PUT", "HEAD"]
    ),
    dynamicDomainPoolHashRing: {
        algorithm: process.env.DYNAMIC_DOMAIN_POOL_HASH_RING_ALGORITHM || "md5",
        vNodeCount: +process.env.DYNAMIC_DOMAIN_POOL_HASH_RING_VNODES || 40,
        replicas: +process.env.DYNAMIC_DOMAIN_POOL_HASH_RING_REPLICAS || 4,
        maxCacheSize: +process.env.DYNAMIC_DOMAIN_POOL_HASH_RING_MAX_CACHE_SIZE || 5000
    },

    playerBonuses: {
        recentPlayerBonusesTTL: +process.env.RECENT_PLAYER_BONUSES_TTL || 86400, // seconds, default one day
        kafka: {
            topicName: process.env.PENDING_PLAYER_BONUSES_TOPIC_NAME || "pending-player-bonuses",
            kafkaBrokerHostnames: process.env.PENDING_PLAYER_BONUSES_KAFKA_BROKERS || "kafka:9092",
            ackTimeoutMs: +process.env.PENDING_PLAYER_BONUSES_KAFKA_ACK_TIMEOUT || 1000,
            clientCreationTimeout: +process.env.PENDING_PLAYER_BONUSES_KAFKA_CLIENT_CREATION_TIMEOUT || 6000,
            requestTimeout: +process.env.PENDING_PLAYER_BONUSES_KAFKA_REQUEST_TIMEOUT || 1000,
            maxSendAttemptTimeout: +process.env.PENDING_PLAYER_BONUSES_KAFKA_MAX_ATTEMPT_TIMEOUT || 10000
        }
    }
};

export default config;
