import { Model, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from "sequelize";
import { sequelize as db } from "../storage/db";
import { DynamicDomain, DomainStatus, RawDomainInfo, DomainInfo } from "../entities/domain";
import { getDomainInfo, setDomainInfo } from "../utils/domain";

export class DynamicDomainModel extends Model<
    InferAttributes<DynamicDomainModel>,
    InferCreationAttributes<DynamicDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string;
    declare description: string | null;
    declare provider: string | null;
    declare status: DomainStatus;
    declare expiryDate: Date | null;
    declare info: DomainInfo | null;
    declare environment: string | null;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): DynamicDomain {
        return {
            id: this.id,
            domain: this.domain,
            description: this.description,
            provider: this.provider,
            status: this.status,
            expiryDate: this.expiryDate,
            info: this.info,
            environment: this.environment,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }
}

DynamicDomainModel.init({
    id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true
    },
    domain: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
    },
    description: DataTypes.TEXT,
    provider: DataTypes.STRING,
    status: {
        type: DataTypes.ENUM(...Object.values(DomainStatus)),
        allowNull: false,
        defaultValue: DomainStatus.ACTIVE
    },
    expiryDate: {
        field: "expiry_date",
        type: DataTypes.DATE
    },
    info: {
        type: DataTypes.JSONB,
        get() {
            return getDomainInfo(this.getDataValue("info") as unknown as RawDomainInfo);
        },
        set(value: DomainInfo) {
            this.setDataValue("info", setDomainInfo(value) as unknown as DomainInfo);
        }
    },
    environment: {
        type: DataTypes.STRING,
        allowNull: false
    },
    createdAt: {
        field: "created_at",
        type: DataTypes.DATE
    },
    updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE
    },
}, {
    tableName: "dynamic_domains",
    sequelize: db,
    underscored: true,
});

export function getDynamicDomainModel() {
    return DynamicDomainModel;
}
