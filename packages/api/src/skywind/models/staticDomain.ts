import { Model, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from "sequelize";
import { sequelize as db } from "../storage/db";
import { StaticDomain, DomainStatus, StaticDomainType, RawDomainInfo, DomainInfo } from "../entities/domain";
import { getDomainInfo, setDomainInfo } from "../utils/domain";

export class StaticDomainModel extends Model<
    InferAttributes<StaticDomainModel>,
    InferCreationAttributes<StaticDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string;
    declare description: string | null;
    declare provider: string | null;
    declare status: DomainStatus;
    declare type: StaticDomainType;
    declare expiryDate: Date | null;
    declare info: DomainInfo | null;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): StaticDomain {
        return {
            id: this.id,
            domain: this.domain,
            description: this.description,
            provider: this.provider,
            status: this.status,
            type: this.type,
            expiryDate: this.expiryDate,
            info: this.info,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }
}

StaticDomainModel.init({
    id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true
    },
    domain: {
        type: DataTypes.STRING, allowNull: false, unique: true
    },
    description: DataTypes.TEXT,
    provider: DataTypes.STRING,
    status: {
        type: DataTypes.ENUM(...Object.values(DomainStatus)),
        allowNull: false,
        defaultValue: DomainStatus.ACTIVE
    },
    type: {
        type: DataTypes.ENUM(...Object.values(StaticDomainType)),
        allowNull: false,
        defaultValue: StaticDomainType.STATIC
    },
    expiryDate: {
        field: "expiry_date",
        type: DataTypes.DATE
    },
    info: {
        type: DataTypes.JSONB,
        get() {
            return getDomainInfo(this.getDataValue("info") as unknown as RawDomainInfo);
        },
        set(value) {
            this.setDataValue("info", setDomainInfo(value) as unknown as DomainInfo);
        }
    },
    createdAt: {
        field: "created_at",
        type: DataTypes.DATE
    },
    updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE
    },
}, {
    tableName: "static_domains",
    sequelize: db,
    underscored: true,
});

export function getStaticDomainModel() {
    return StaticDomainModel;
}
