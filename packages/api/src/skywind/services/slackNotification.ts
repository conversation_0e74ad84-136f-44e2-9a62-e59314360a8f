import { logging } from "@skywind-group/sw-utils";
import { createHttpClient, HttpClient } from "../utils/httpClient";
import config from "../config";

const log = logging.logger("slack-notification");

export interface SlackMessage {
    text: string;
    channel?: string;
    username?: string;
    icon_emoji?: string;
}

export interface BlockedDomainAlert {
    domain: string;
    blockedAt: Date;
    poolId?: number;
    poolName?: string;
    reason?: string;
}

export interface EmptyPoolAlert {
    poolId: number;
    poolName: string;
    poolType: "static" | "dynamic";
}

export interface DefaultDomainBlockedAlert {
    domain: string;
    blockedAt: Date;
    domainType: "static" | "dynamic" | "lobby" | "liveStreaming" | "ehub";
}

export class SlackNotificationService {
    private readonly httpClient: HttpClient;
    private readonly webhookUrl: string;
    private readonly enabled: boolean;

    constructor() {
        this.enabled = config.slackNotifications.enabled;
        this.webhookUrl = config.slackNotifications.blockedDomainsWebhookUrl;
        
        if (this.enabled && !this.webhookUrl) {
            log.warn("Slack notifications are enabled but webhook URL is not configured");
            this.enabled = false;
        }

        this.httpClient = createHttpClient({
            timeout: config.slackNotifications.timeout,
            retryConfig: config.slackNotifications.retryConfig,
            headers: {
                "Content-Type": "application/json"
            }
        }, log);
    }

    public async sendBlockedDomainAlert(alert: BlockedDomainAlert): Promise<void> {
        if (!this.enabled) {
            log.debug("Slack notifications disabled, skipping blocked domain alert");
            return;
        }

        const message = this.formatBlockedDomainMessage(alert);
        await this.sendMessage(message);
    }

    public async sendEmptyPoolAlert(alert: EmptyPoolAlert): Promise<void> {
        if (!this.enabled) {
            log.debug("Slack notifications disabled, skipping empty pool alert");
            return;
        }

        const message = this.formatEmptyPoolMessage(alert);
        await this.sendMessage(message);
    }

    public async sendDefaultDomainBlockedAlert(alert: DefaultDomainBlockedAlert): Promise<void> {
        if (!this.enabled) {
            log.debug("Slack notifications disabled, skipping default domain blocked alert");
            return;
        }

        const message = this.formatDefaultDomainBlockedMessage(alert);
        await this.sendMessage(message);
    }

    private formatBlockedDomainMessage(alert: BlockedDomainAlert): SlackMessage {
        const timestamp = alert.blockedAt.toISOString();
        let text = "🚫 *Domain Blocked Alert*\n";
        text += `*Domain:* ${alert.domain}\n`;
        text += `*Blocked At:* ${timestamp}\n`;

        if (alert.poolName) {
            text += `*Pool:* ${alert.poolName} (ID: ${alert.poolId})\n`;
        }

        if (alert.reason) {
            text += `*Reason:* ${alert.reason}\n`;
        }

        return {
            text,
            channel: "#blocked_domains",
            username: "Domain Monitor",
            icon_emoji: ":warning:"
        };
    }

    private formatEmptyPoolMessage(alert: EmptyPoolAlert): SlackMessage {
        let text = "⚠️ *Empty Domain Pool Alert*\n";
        text += `*Pool:* ${alert.poolName} (ID: ${alert.poolId})\n`;
        text += `*Pool Type:* ${alert.poolType}\n`;
        text += `*Status:* No active, unblocked domains available\n`;

        return {
            text,
            channel: "#blocked_domains",
            username: "Domain Monitor",
            icon_emoji: ":exclamation:"
        };
    }

    private formatDefaultDomainBlockedMessage(alert: DefaultDomainBlockedAlert): SlackMessage {
        const timestamp = alert.blockedAt.toISOString();
        let text = "🔴 *Default Domain Blocked Alert*\n";
        text += `*Domain:* ${alert.domain}\n`;
        text += `*Domain Type:* ${alert.domainType}\n`;
        text += `*Blocked At:* ${timestamp}\n`;
        text += "*Impact:* Default domain is no longer accessible\n";

        return {
            text,
            channel: "#blocked_domains",
            username: "Domain Monitor",
            icon_emoji: ":rotating_light:"
        };
    }

    private async sendMessage(message: SlackMessage): Promise<void> {
        try {
            log.info({ message }, "Sending Slack notification");
            
            await this.httpClient.post(this.webhookUrl, message);
            
            log.info("Slack notification sent successfully");
        } catch (error) {
            log.error({ error: error.message, message }, "Failed to send Slack notification");
            throw error;
        }
    }
}

let slackNotificationService: SlackNotificationService;

export function getSlackNotificationService(): SlackNotificationService {
    if (!slackNotificationService) {
        slackNotificationService = new SlackNotificationService();
    }
    return slackNotificationService;
}
