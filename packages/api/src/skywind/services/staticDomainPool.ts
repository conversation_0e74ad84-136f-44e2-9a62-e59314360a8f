import { FindOptions, Op, Transaction } from "sequelize";
import { StaticDomainPoolDBInstance } from "../models/staticDomainPool";
import { lazy } from "@skywind-group/sw-utils";
import * as Errors from "../errors";
import {
    StaticDomainPoolService,
    StaticDomainPoolAttributes,
    StaticDomainPoolItemAttributes,
    DomainPoolCreateData
} from "../entities/domainPool";
import { sequelize as db } from "../storage/db";
import { Models } from "../models/models";
import * as StaticDomainPoolCache from "../cache/staticDomainPoolCache";

const StaticDomainPoolItemModel = Models.StaticDomainPoolItemModel;
const StaticDomainPoolModel = Models.StaticDomainPoolModel;
const StaticDomainModel = Models.StaticDomainModel;

export const domainPoolAssociations: FindOptions<any>["include"] = [
    {
        model: StaticDomainModel,
        as: "domains",
        through: {
            attributes: ["isActive", "blockedDate"]
        }
    }
];

function toInfo(domainPool: StaticDomainPoolDBInstance): StaticDomainPoolAttributes {

    const { domains, ...result } = domainPool.toJSON();

    return {
        ...result,
        domains: domains?.map(({ StaticDomainPoolItem, ...domain }) => {
            if (StaticDomainPoolItem) {
                domain.isActive = StaticDomainPoolItem.isActive;
                domain.blockedDate = StaticDomainPoolItem.blockedDate;
            }
            return domain;
        }) || []
    };
}

function validateDomainsExist(domainIds: number[], foundDomainIds: number[]) {
    const foundIds = new Set(foundDomainIds);
    const missingIds = domainIds.filter(id => !foundIds.has(id));
    if (missingIds.length > 0) {
        throw new Errors.DomainNotFoundError(missingIds[0]);
    }
}

async function validateStaticDomainsExist(domains: { id: number }[], transaction: Transaction) {
    const domainIds = domains.map(({ id }) => id);
    validateDomainsExist(domainIds, (await StaticDomainModel.findAll({
        where: { id: { [Op.in]: domainIds } },
        transaction
    })).map<number>(item => item.get("id")));
}

class StaticDomainPoolServiceImpl implements StaticDomainPoolService {

    public async create(data: DomainPoolCreateData): Promise<StaticDomainPoolAttributes> {
        return db.transaction(async transaction => {
            const existing = await StaticDomainPoolModel.findOne(
                {
                    where: {
                        name: data.name
                    },
                    transaction
                }
            );

            if (existing) {
                throw new Errors.DomainPoolAlreadyExistsError();
            }

            const created = await StaticDomainPoolModel.create({
                name: data.name,
                domainWatcherAdapterId: data.domainWatcherAdapterId
            }, { transaction });
            const staticDomainPoolId = created.get("id") as number;

            if (data.domains && data.domains.length > 0) {
                await validateStaticDomainsExist(data.domains, transaction);
                await StaticDomainPoolItemModel.bulkCreate(data.domains.map(domain => ({
                    staticDomainPoolId,
                    staticDomainId: domain.id,
                    isActive: domain.isActive ?? true
                })), { transaction });
            }

            const domainPoolWithAssociations = await StaticDomainPoolModel.findByPk(staticDomainPoolId, {
                include: domainPoolAssociations,
                transaction
            });

            return toInfo(domainPoolWithAssociations);
        });
    }

    public async update(staticDomainPoolId: number,
        data: Partial<DomainPoolCreateData>): Promise<StaticDomainPoolAttributes> {

        return db.transaction(async transaction => {
            const domainPool = await StaticDomainPoolModel.findByPk(staticDomainPoolId, { transaction });
            if (!domainPool) {
                throw new Errors.DomainPoolNotFoundError();
            }

            if (data?.name || data?.domainWatcherAdapterId !== undefined) {
                const updateData: any = {};
                if (data?.name) {
                    updateData.name = data.name;
                }
                if (data?.domainWatcherAdapterId !== undefined) {
                    updateData.domainWatcherAdapterId = data.domainWatcherAdapterId;
                }
                await domainPool.update(updateData, { transaction });
            }

            if (data?.domains) {
                await validateStaticDomainsExist(data.domains, transaction);

                await StaticDomainPoolItemModel.destroy({ where: { staticDomainPoolId }, transaction });
                await StaticDomainPoolItemModel.bulkCreate(data.domains.map(domain => ({
                    staticDomainPoolId,
                    staticDomainId: domain.id,
                    isActive: domain.isActive ?? true
                })), { transaction });
            }

            const domainPoolWithAssociations = await StaticDomainPoolModel.findByPk(staticDomainPoolId, {
                include: domainPoolAssociations,
                transaction
            });

            const info = toInfo(domainPoolWithAssociations);
            StaticDomainPoolCache.reset(staticDomainPoolId);

            return info;
        });
    }

    public async findById(id: number): Promise<StaticDomainPoolAttributes> {
        const domainPool = await StaticDomainPoolCache.findOne(id);
        if (!domainPool) {
            throw new Errors.DomainPoolNotFoundError();
        }

        return toInfo(domainPool);
    }

    public async findAll(findOptions?: FindOptions<unknown>): Promise<StaticDomainPoolAttributes[]> {
        const domainPools = await StaticDomainPoolModel.findAll({
            ...findOptions,
            include: domainPoolAssociations
        });
        return domainPools.map(toInfo);
    }

    public async remove(id: number): Promise<void> {
        const domainPool = await StaticDomainPoolModel.findByPk(id);
        if (!domainPool) {
            throw new Errors.DomainPoolNotFoundError();
        }
        await StaticDomainPoolModel.destroy({ where: { id } });
        StaticDomainPoolCache.reset(id);
    }

    public async addDomain(staticDomainPoolId: number, staticDomainId: number): Promise<StaticDomainPoolItemAttributes> {
        const staticDomain = await StaticDomainModel.findByPk(staticDomainId);
        if (!staticDomain) {
            throw new Errors.DomainNotFoundError(staticDomainId);
        }

        let item = await StaticDomainPoolItemModel.findOne({
            where: {
                staticDomainId,
                staticDomainPoolId
            }
        });
        if (item) {
            throw new Errors.DomainPoolItemAlreadyExistsError();
        }

        item = await StaticDomainPoolItemModel.create({
            staticDomainId,
            staticDomainPoolId
        });

        const result = item.toJSON();
        StaticDomainPoolCache.reset(staticDomainPoolId);

        return result;
    }

    public async removeDomain(staticDomainPoolId: number, staticDomainId: number): Promise<void> {
        const existing = await StaticDomainPoolItemModel.findOne({
            where: {
                staticDomainId,
                staticDomainPoolId
            }
        });
        if (!existing) {
            throw new Errors.DomainPoolItemNotFoundError();
        }

        await StaticDomainPoolItemModel.destroy({
            where: {
                staticDomainId,
                staticDomainPoolId
            }
        });
        StaticDomainPoolCache.reset(staticDomainPoolId);
    }

    public disableDomain(poolId: number, domainId: number): Promise<void> {
        return this.updatePoolItemStatus(poolId, domainId, { isActive: false });
    }

    public enableDomain(poolId: number, domainId: number): Promise<void> {
        return this.updatePoolItemStatus(poolId, domainId, { isActive: true });
    }

    public setBlocked(poolId: number, domainId: number, blockedDate?: Date | null): Promise<void> {
        return this.updatePoolItemStatus(poolId, domainId, { blockedDate: blockedDate });
    }

    public countDomains(poolId: number): Promise<number> {
        return StaticDomainPoolItemModel.count({
            include: [{
                model: StaticDomainModel,
                where: {
                    status: "active"
                },
                required: true
            }],
            where: {
                staticDomainPoolId: poolId,
                isActive: true,
                blockedDate: null
            }
        });
    }

    private async updatePoolItemStatus(staticDomainPoolId: number, staticDomainId: number, values: { isActive?: boolean; blockedDate?: Date | null }) {
        const staticDomain = await StaticDomainModel.findByPk(staticDomainId);
        if (!staticDomain) {
            throw new Errors.DomainNotFoundError(staticDomainId);
        }

        const existing = await StaticDomainPoolItemModel.findOne({
            where: {
                staticDomainId,
                staticDomainPoolId
            }
        });
        if (!existing) {
            throw new Errors.DomainPoolItemNotFoundError();
        }

        await StaticDomainPoolItemModel.update(
            values,
            {
                where: {
                    staticDomainId,
                    staticDomainPoolId
                }
            }
        );
        StaticDomainPoolCache.reset(staticDomainPoolId);
    }
}

const staticDomainPoolService = lazy<StaticDomainPoolService>(() => new StaticDomainPoolServiceImpl());

export const getStaticDomainPoolService = () => staticDomainPoolService.get();
