import {
    CreateOptions,
    FindAndCountOptions,
    FindOptions,
    IncludeOptions,
    literal,
    Op,
    Transaction,
    UniqueConstraintError,
    WhereOptions
} from "sequelize";
import * as Errors from "../errors";
import {
    BulkAddEntityGamesResult,
    BulkUpsertGamesRtp,
    EntityGame,
    EntityGameCodeInfo,
    EntityGameData,
    EntityGameFeatures,
    EntityGameInfo,
    EntityGameUrlParams,
    FeaturesRTP,
    FindOneGameOptions,
    Game,
    GameClientFeatures,
    GameCodeInfo,
    GameCodesData,
    GameDescription,
    GameDescriptionByLocale,
    GameFeatures,
    GameInfo,
    GameProviderInfo,
    GameSettings,
    JackpotMapping,
    LimitFiltersByCurrency,
    LiveRush,
    LiveTable,
    Marketing,
    ProviderGameCodeInfo,
} from "../entities/game";
import { BaseEntity, ChildEntity, Entity, ENTITY_TYPE, FindEntityOptions } from "../entities/entity";
import { EntityGameDBInstance, GameDBInstance, } from "../models/game";
import { sequelize as db } from "../storage/db";
import { Limits, LimitsByCurrencyCode, SlotGameLimits } from "../entities/gamegroup";
import { GameProvider } from "../entities/gameprovider";
import { Label } from "../entities/label";
import { GameProviderImpl } from "./gameprovider";
import { LabelDBInstance } from "../models/label";
import * as LabelService from "../services/label";
import * as FilterService from "./filter";
import { mapFilter, parseFilter, valueFromQuery } from "./filter";
import { LabelImpl } from "./label";
import { PagingHelper, PagingInfo } from "../utils/paginghelper";
import * as JackpotService from "./jackpot";
import {
    CANVAS_RENDER,
    ENTITY_GAME_STATUS,
    GAME_TYPES,
    HISTORY_RENDER_TYPE,
    mergeArrayToObject,
    SORT_ORDER,
    STAKED_GAMES_TYPES,
    SUPER_ADMIN_ID,
    validatePositiveNumber
} from "../utils/common";
import { GameCategory } from "../entities/gamecategory";
import { filterGameLimits, findGameLimitsByCurrencyCode, findPlayerLimits, validateLimitFilters } from "./limits";
import { isForbiddenAlignLimits } from "../models/gamegrouplimit";
import { LiveManagerService } from "./live";
import { buildItgHistoryUrl, buildSwHistoryUrl } from "../history/gameHistory";
import { getEntitySettings } from "./settings";
import { parse } from "url";
import { getParentIds } from "./entity";
import { EntitySettings } from "../entities/settings";
import { getGameLimitsConfigurationService } from "./gameLimits/gameLimitsConfiguration";
import { validateTranslations } from "../utils/validateTranslations";
import {
    FavoriteGamesCache,
    favoriteGamesCache,
    gameCategoryGamesCache,
    GameCategoryGamesService
} from "./gameCategory/gameCategoryGamesService";
import { GameCategoryItemsService } from "./gameCategory/gameCategoryItemsService";
import { GameCategoryService } from "./gameCategory/gameCategoryService";
import { gameRTPHistoryService, rtpService } from "./gameRTPHistory";
import { getCurrencyExchange } from "./currencyExchange";
import * as LobbyCache from "../cache/lobby";
import { encodeId } from "../utils/publicid";
import { GameFinalizationType } from "@skywind-group/sw-wallet-adapter-core";
import { getNewLimitsFacade } from "./gameLimits/limitsFacade";
import { getGameGroupId } from "./playService";
import { BrandEntity } from "../entities/brand";
import { Models } from "../models/models";
import { cloneDeep, isEmpty, isEqual, merge } from "lodash";
import { isLiveRush } from "./games/liveGame";
import { buildDynamicLiveManagerUrl } from "./entityDynamicDomainService";
import { IoServerV4 } from "../io-versions";
import config from "../config";

type GamesList = EntityGame[] & PagingInfo[];

const sortableKeys = ["code", "title", "providerTitle", "providerCode", "categoryList", "releaseDate"];
const DEFAULT_SORT_KEY = "title";
const RELEASE_DATE_FIELD = "game.releaseDate";
const RELEASE_DATE_SORT_KEY = "game.createdAt";

const GameModel = Models.GameModel;
const GameProviderModel = Models.GameProviderModel;
const EntityGameModel = Models.EntityGameModel;
const LabelModel = Models.LabelModel;
const FavoriteGamesModel = Models.FavoriteGameModel;
const GameGroupLimitModel = Models.GameGroupLimitModel;

const ADD_GAMES_SQL: string = "SELECT po_games_added AS count FROM " +
    "fnc_add_entity_games( p_entity_id => :entityId, p_game_codes => :gameCodes::VARCHAR[])";

export interface GameModelAttributesToExclude {
    related?: string[];
    main?: string[];
}

interface GetOneGameOptions {
    addAggregatedFinalLimits?: boolean;
    skipJurisdictionFiltering?: boolean;
    currency?: string;
    currencies?: string[];
    gameGroupName?: string;
    segmentId?: number;
}

const DEFAULT_GAME_MODEL_ATTRIBUTES_TO_EXCLUDE = {
    related: ["secret"],
    main: ["url"]
};

export class GameImpl implements Game {
    public static SEARCHABLE_KEYS = [
        "code",
        "providerGameCode",
        "title",
        "type",
        "providerId",
        "schemaDefinitionId",
        "physicalTableId"
    ];

    public id: number;
    public code: string;
    public title: string;
    public type: string;
    public url: string;
    public status: string;
    public providerId: number;
    public providerGameCode: string;
    public defaultInfo: GameDescription;
    public info: GameDescriptionByLocale;
    public limits: LimitsByCurrencyCode;
    public labels: Label[];
    public comment: string;
    public gameProvider: GameProvider;
    public features: GameFeatures;
    public clientFeatures: GameClientFeatures;
    private version: number = 0;
    public historyRenderType: number = HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME;
    public historyUrl: string;
    public createdAt: string;
    public limitsGroup: string;
    public countries: string[];
    public totalBetMultiplier: number;
    public schemaDefinitionId: number;
    public deploymentGroupId?: number;
    public defaultClientVersion?: string;
    public physicalTableId: string;

    constructor(item?: GameDBInstance) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.code = item.get("code");
        this.title = item.get("title");
        this.type = item.get("type");
        this.url = item.get("url");
        this.status = item.get("status");
        this.providerId = item.get("providerId");
        this.providerGameCode = item.get("providerGameCode");
        this.defaultInfo = item.get("defaultInfo");
        this.info = item.get("info");
        this.limits = item.get("limits");
        this.comment = item.get("comment");
        this.features = item.get("features") || {};
        this.clientFeatures = item.get("clientFeatures") || {};
        this.version = item.get("version");
        this.createdAt = item.get("createdAt");
        this.limitsGroup = item.get("limitsGroup");
        this.countries = item.get("countries");
        this.totalBetMultiplier = item.get("totalBetMultiplier");
        this.schemaDefinitionId = item.get("schemaDefinitionId");
        this.deploymentGroupId = item.get("deploymentGroupId");
        this.defaultClientVersion = item.get("defaultClientVersion");
        this.physicalTableId = item.get("physicalTableId");

        const gameProviderDB = item.get("gameProvider");
        if (gameProviderDB) {
            this.gameProvider = new GameProviderImpl(gameProviderDB);
        }
        const labelDB = item.get("labels");
        if (labelDB) {
            this.labels = labelDB.map(
                (label: LabelDBInstance) => new LabelImpl(label).toPublicInfo()
            );
        }
        this.historyRenderType = item.get("historyRenderType");
        this.historyUrl = item.get("historyUrl");
    }

    public async save(transaction?: Transaction): Promise<this> {
        const oldVersion = this.version;
        this.version = oldVersion + 1;

        try {
            const updateResult: [number] = await GameModel.update(this as any, {
                where: {
                    id: { [Op.eq]: this.id },
                    version: { [Op.eq]: oldVersion },
                },
                transaction: transaction
            });
            if (updateResult[0] !== 1) {
                return Promise.reject(new Errors.OptimisticLockException());
            }
        } catch (err) {
            this.version = oldVersion;
            return Promise.reject(err);
        } finally {
            await LobbyCache.reset();
            await favoriteGamesCache.reset();
        }

        return this;
    }

    public toCodeInfo(): GameCodeInfo {
        return {
            code: this.code,
        };
    }

    public toProviderGameCodeInfo(): ProviderGameCodeInfo {
        return {
            providerGameCode: this.providerGameCode,
        };
    }

    public toProviderInfo(): GameProviderInfo {
        return {
            providerGameCode: this.providerGameCode,
            title: this.title,
            type: this.type,
            url: this.url,
            defaultInfo: this.defaultInfo,
            info: this.info,
            limits: this.limits,
            providerCode: this.gameProvider ? this.gameProvider.code : null,
            providerTitle: this.gameProvider ? this.gameProvider.title : null,
            features: this.features,
            clientFeatures: this.clientFeatures,
            historyRenderType: this.historyRenderType,
            historyUrl: this.historyUrl,
            limitsGroup: this.limitsGroup,
            countries: this.countries,
            totalBetMultiplier: typeof this.totalBetMultiplier === "string" ? +this.totalBetMultiplier : null,
            schemaDefinitionId: this.schemaDefinitionId,
            defaultClientVersion: this.defaultClientVersion
        };
    }

    public toInfo(): GameInfo {
        return {
            code: this.code,
            title: this.title,
            type: this.type,
            defaultInfo: this.defaultInfo,
            info: this.info,
            limits: this.limits,
            labels: this.labels,
            providerCode: this.gameProvider ? this.gameProvider.code : null,
            providerTitle: this.gameProvider ? this.gameProvider.title : null,
            features: this.features,
            clientFeatures: this.clientFeatures,
            historyRenderType: this.historyRenderType,
            historyUrl: this.historyUrl,
            releaseDate: this.createdAt,
            limitsGroup: this.limitsGroup,
            countries: this.countries,
            totalBetMultiplier: typeof this.totalBetMultiplier === "string" ? +this.totalBetMultiplier : null,
            schemaDefinitionId: this.schemaDefinitionId,
            providerGameCode: this.providerGameCode,
            url: this.url,
            physicalTableId: this.physicalTableId ? encodeId(+this.physicalTableId) : undefined
        };
    }

    // mode parameter can be provided by BO to get historyUrl instead of historyUrl2
    public async getHistoryUrl(entity: BaseEntity, mode?: string): Promise<string> {
        if (this.historyUrl) {
            return buildSwHistoryUrl(this.historyUrl, entity, this);
        }

        if (isItgMiniGsGame(this) && mode && mode === "bo") {
            return buildItgHistoryUrl(this, entity);
        }

        if (CANVAS_RENDER.includes(this.historyRenderType) || (mode && mode === "bo")) {
            const urlDetail = parse(await buildSwHistoryUrl(this.url, entity, this));
            return `${urlDetail.protocol}//${urlDetail.hostname}${urlDetail.pathname}`.replace("index", "history");
        } else {
            const settings = await getEntitySettings(entity.path);
            const historyKeyName = this.historyRenderType < 3 ? "history_url" : "history2_url";
            return buildSwHistoryUrl(settings?.urlParams?.[historyKeyName] as string, entity, this);
        }
    }

    public async getGameHistoryModuleUrl(entity: BaseEntity): Promise<string> {
        if (this.historyUrl) {
            return buildSwHistoryUrl(this.historyUrl, entity, this);
        }

        if (isItgMiniGsGame(this)) {
            return buildItgHistoryUrl(this, entity);
        } else {
            const urlDetail = parse(await buildSwHistoryUrl(this.url, entity, this));
            return `${urlDetail.protocol}//${urlDetail.hostname}${urlDetail.pathname}`.replace("index", "history");
        }
    }

    public get limitFiltersWillBeApplied(): boolean {
        const supported = this.features.decreaseMaxBetSupported || this.features.increaseMinBetSupported;
        return !!(this.totalBetMultiplier &&
            supported &&
            this.features.highestWin &&
            (this.type === GAME_TYPES.slot || this.type === GAME_TYPES.external));
    }

    public isLiveGame(): boolean {
        return this.type === GAME_TYPES.live;
    }

    public async getLimits(currency: string, copyLimitsFrom?: string): Promise<Limits> {
        let limits: Limits;

        if (this.limits) {
            limits = this.limits[currency];
        }

        if (!limits) {
            if (copyLimitsFrom && this.limits && this.limits[copyLimitsFrom]) {
                limits = this.limits[copyLimitsFrom];
            } else {
                return;
            }
        }

        if (isForbiddenAlignLimits(this.type, currency)) {
            return limits;
        }

        const slotLimits = limits as SlotGameLimits;
        const slotLimitsInEUR = this.limits["EUR"] as SlotGameLimits;

        if (slotLimits && !slotLimits.winMax && slotLimitsInEUR && slotLimitsInEUR.winMax) {
            const service = await getCurrencyExchange();
            slotLimits.winMax = service.exchange(slotLimitsInEUR.winMax, "EUR", currency);
        }

        return slotLimits;
    }
}

export class EntityGameImpl implements EntityGame {
    public id: number;
    public entityId: number;
    public gameId: number;
    public parentEntityGameId: number;
    public settings: GameSettings;
    public status: string;
    public game: Game;
    public releaseDate: string;
    public limitFilters?: LimitFiltersByCurrency;
    public urlParams?: EntityGameUrlParams;
    public externalGameId?: string;
    public parentGame?: EntityGame;
    public domain?: string;
    public title?: string;
    public features?: EntityGameFeatures;

    constructor(item?: EntityGameDBInstance) {
        if (!item) {
            return;
        }
        this.id = item.get("id");
        this.entityId = item.get("entityId");
        this.gameId = item.get("gameId");
        this.settings = item.get("settings");
        this.status = item.get("status");
        this.limitFilters = item.get("limitFilters");
        this.urlParams = item.get("urlParams");
        this.externalGameId = item.get("externalGameId");
        this.parentEntityGameId = item.get("parentEntityGameId");
        this.title = item.get("title");
        this.domain = item.get("domain");
        this.features = item.get("features");

        this.game = new GameImpl(item.get("game"));
        if (this.game) {
            this.releaseDate = this.game.createdAt;
        }
    }

    public setParentGame(parentGame: EntityGame): void {
        this.parentGame = parentGame;
    }

    public isSuspended(): boolean {
        return this.status === "suspended";
    }

    public toInfo(isLobby?: boolean, addGameStatus?: boolean): EntityGameInfo {
        const info: EntityGameInfo = this.game.toInfo();
        if (isLobby) {
            info.id = this.id;
            info.gameId = this.game.id;
        }
        if (addGameStatus) {
            info.gameStatus = this.game.status;
        }

        if (this.status) {
            info.status = this.status;
        }
        if (this.settings) {
            info.settings = this.settings;
        }
        if (info.limits && this.limitFilters && STAKED_GAMES_TYPES.includes(this.game.type)) {
            info.limitFilters = this.limitFilters;
            info.limits = filterGameLimits(info.type, this.limitFilters, this.game.limits);
        }
        if (this.urlParams) {
            info.urlParams = this.urlParams;
        }
        if (this.domain) {
            info.domain = this.domain;
        }
        if (this.game.features) {
            const gameFeatures = this.game.features;
            const rtpConfiguratorSettings = this.settings?.rtpConfigurator || {};
            const objectRtpService = rtpService.get();
            info.rtpInfo = {
                rtpTheoretical: objectRtpService.rtpTheoretical(gameFeatures, rtpConfiguratorSettings),
                rtpDeduction: objectRtpService.rtpDeduction(rtpConfiguratorSettings),
                rtpFinal: objectRtpService.rtpFinal(gameFeatures, rtpConfiguratorSettings),
            };

            info.limitFiltersWillBeApplied = this.limitFiltersWillBeApplied;
        }

        if (this.externalGameId) {
            info.externalGameId = this.externalGameId;
        }

        if (this.title) {
            info.title = this.title;
        }
        const translations = this.features?.translations;
        if (translations) {
            info.features = merge(info.features || {}, { translations });
        }

        return info;
    }

    public toCodeInfo(): EntityGameCodeInfo {
        const info: EntityGameCodeInfo = this.game.toCodeInfo();
        if (this.status) {
            info.status = this.status;
        }
        if (this.settings) {
            info.settings = this.settings;
        }
        if (this.limitFilters && STAKED_GAMES_TYPES.includes(this.game.type)) {
            info.limitFilters = this.limitFilters;
        }
        if (this.urlParams) {
            info.urlParams = this.urlParams;
        }
        if (this.title) {
            info.title = this.title;
        }
        if (this.features) {
            info.features = this.features;
        }
        if (this.domain) {
            info.domain = this.domain;
        }
        return info;
    }

    public settingsReady(): boolean {
        if (this.game.features.ignoreJackpotTypesValidation) {
            return true;
        }
        if (this.game.features && this.game.features.jackpotTypes) {
            if (!this.settings || !this.settings.jackpotId) {
                return false;
            }
            for (const type of this.game.features.jackpotTypes) {
                if (!this.settings.jackpotId[type]) {
                    return false;
                }
            }
        }
        return true;
    }

    public isLiveGame(): boolean {
        return this.game.isLiveGame();
    }

    public get decreaseMaxBetSupported(): boolean {
        if (this.settings && "decreaseMaxBetSupported" in this.settings) {
            return this.settings.decreaseMaxBetSupported;
        }

        return this.game.features.decreaseMaxBetSupported;
    }

    public get increaseMinBetSupported(): boolean {
        if (this.settings && "increaseMinBetSupported" in this.settings) {
            return this.settings.increaseMinBetSupported;
        }

        return this.game.features.increaseMinBetSupported;
    }

    public get limitFiltersWillBeApplied(): boolean {
        const supported = this.decreaseMaxBetSupported || this.increaseMinBetSupported;
        return !!(this.game.totalBetMultiplier &&
            supported &&
            this.game.features.highestWin &&
            (this.game.type === GAME_TYPES.slot || this.game.type === GAME_TYPES.external));
    }
}

/**
 * getAllGames - Gets all available games for entity
 *
 * @param keyEntity
 * @param entityOptions
 * @param requestQuery
 * @param relativeUrl
 * @returns {Promise<EntityGameInfo[]>}
 */
export async function getAllGames(keyEntity: BaseEntity,
                                  entityOptions: FindEntityOptions = {},
                                  requestQuery: any = {},
                                  relativeUrl?: string): Promise<EntityGameInfo[]> {

    const entity: BaseEntity = !isEmpty(entityOptions) ? keyEntity.find(entityOptions) : keyEntity;
    if (!entity) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }
    if (entity.isSuspended() && !keyEntity.isMaster()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }

    const queries = new Map();

    const filterConditions = parseFilter(requestQuery, ["limit", "offset", "sortBy", "sortOrder"]);
    filterConditions["sortBy"] = FilterService.getSortKey(filterConditions, sortableKeys, DEFAULT_SORT_KEY);
    filterConditions["sortOrder"] = FilterService.valueFromQuery(filterConditions, "sortOrder") || "ASC";
    queries.set("filter", filterConditions);

    queries.set("game", parseFilter(requestQuery, GameImpl.SEARCHABLE_KEYS));

    queries.set("gameProvider", mapFilter(requestQuery, {
        providerCode: "code",
        providerTitle: "title",
    }));

    if (requestQuery["gamecategoryId__in"]) {
        queries.set("gameCategory", { ids: requestQuery["gamecategoryId__in"].toString().split(",") });
    } else if (requestQuery["gamecategoryId"]) {
        queries.set("gameCategory", { ids: [requestQuery["gamecategoryId"]] });
    } else if (requestQuery["onlyCategoryGames"] === "true") {
        const categories = await new GameCategoryService(keyEntity).findAll();
        queries.set("gameCategory", { ids: categories.map(category => category.id) });
    }

    if (requestQuery["status"]) {
        queries.set("entity", { status: requestQuery["status"] });
    } else if (requestQuery["excludeInactiveGames"] === "true" || requestQuery["excludeInactiveGames"] === true) {
        queries.set("entity", { status: { [Op.in]: [ENTITY_GAME_STATUS.NORMAL, ENTITY_GAME_STATUS.TEST] } });
    }

    Object.assign(queries.get("game"), parseGameFeaturesFilter(requestQuery));

    queries.set("label", LabelService.prepareSearchQuery(
        parseFilter(requestQuery, LabelService.getSearchableKeys())
    ));

    // case when we need limits only for certain currency
    if (requestQuery["currency"]) {
        queries.set("currency", requestQuery["currency"]);
    }

    const source: GamesList = await findAllEntityGames(entity, queries, requestQuery.shortInfo === "true");
    let target: EntityGameInfo[] = source.map((entityGame: EntityGame) => entityGame.toInfo());
    if ("limitFiltersWillBeApplied" in requestQuery) {
        if (requestQuery.limitFiltersWillBeApplied === "true") {
            target = target.filter(game => game.limitFiltersWillBeApplied);
        } else if (requestQuery.limitFiltersWillBeApplied === "false") {
            target = target.filter(game => !game.limitFiltersWillBeApplied);
        }
    }

    if (requestQuery["jackpots"] === "true") {
        await JackpotService.appendJackpots(entity, target, requestQuery["jpCurrency"] as string);
    }
    if (requestQuery["includeLive"] === "true") {
        const urlManager = await buildDynamicLiveManagerUrl(keyEntity);
        await LiveManagerService.addLiveInfo(urlManager, target, relativeUrl);
    }
    return PagingHelper.copyInfo(target, source);
}

export async function getGameWithJackpot(entity: BaseEntity,
                                         gameCode: string,
                                         jpCurrency?: string): Promise<EntityGameInfo> {
    if (!entity) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }
    if (entity.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }
    const gameInfo: EntityGameInfo = (await findOneEntityGame(entity, gameCode)).toInfo();
    await JackpotService.appendJackpots(entity, [gameInfo], jpCurrency);
    const urlManager = await buildDynamicLiveManagerUrl(entity);
    await LiveManagerService.addLiveInfoToGame(urlManager, gameInfo);
    return gameInfo;
}

export function parseGameFeaturesFilter(requestQuery: any) {
    const filter = {};
    const notFilter = {};

    const features = parseFeaturesJson(requestQuery);

    [
        "isFreebetSupported",
        "isBonusCoinsSupported",
        "transferEnabled",
        "isGRCGame",
        "isMarketplaceSupported",
        "isCustomLimitsSupported",
        "decreaseMaxBetSupported",
        "increaseMinBetSupported"
    ].forEach(
        (booleanFeature) =>
            parseBooleanGameFeature(features, requestQuery[booleanFeature], booleanFeature, filter, notFilter));

    const jackpotTypes = requestQuery["jackpotTypes"];
    if (jackpotTypes !== undefined) {
        if (jackpotTypes === "true" || jackpotTypes === true) {
            filter["jackpotTypes"] = [];
        } else if (jackpotTypes === "false" || jackpotTypes === false) {
            if (features["jackpotTypes"]) {
                throw new Errors.ValidationError("Conditions conflict on field  - jackpotTypes");
            }
            notFilter["jackpotTypes"] = [];
        } else {
            const jackpots: string[] = jackpotTypes.split(",");
            if (features["jackpotTypes"] && !isEqual(features["jackpotTypes"], jackpots)) {
                throw new Errors.ValidationError("Conditions conflict on field  - jackpotTypes");
            }
            filter["jackpotTypes"] = jackpots;
        }
    }

    const live = requestQuery["live"];
    if (live !== undefined) {
        if (live === "true" || live === true) {
            filter["live"] = {};
        } else if (live === "false" || live === false) {
            if (features["live"]) {
                throw new Errors.ValidationError("Conditions conflict on field  - live");
            }
            notFilter["live"] = {};
        } else {
            try {
                filter["live"] = JSON.parse(live);
            } catch (err) {
                throw new Errors.ValidationError("Search filter 'live' is not valid");
            }
            if (features["live"] && !isEqual(features["live"], filter["live"])) {
                throw new Errors.ValidationError("Conditions conflict on field  - live");
            }
        }
    }

    const featuresFilter = {};
    Object.assign(filter, features);
    if (Object.keys(filter).length) {
        featuresFilter["features"] = { [Op.contains]: filter };
    }
    if (Object.keys(notFilter).length) {
        featuresFilter[Op.not] = { features: { [Op.contains]: notFilter } };
    }
    return featuresFilter;
}

export function parseBooleanGameFeature(features: object,
                                        featureValue: any,
                                        booleanFeature: string,
                                        filter: object,
                                        notFilter: object) {

    if (featureValue !== undefined) {
        if (featureValue === "true" || featureValue === true) {
            if (features[booleanFeature] !== undefined && features[booleanFeature] === false) {
                throw new Errors.ValidationError(`Conditions conflict on field  - ${booleanFeature}`);
            }
            filter[booleanFeature] = true;
        } else {
            if (features[booleanFeature] !== undefined && features[booleanFeature] === true) {
                throw new Errors.ValidationError(`Conditions conflict on field  - ${booleanFeature}`);
            }
            notFilter[booleanFeature] = true;

        }
        const featureFilter = features[booleanFeature];
        if (featureFilter !== undefined) {
            if (notFilter[booleanFeature] && featureFilter === true) {
                throw new Errors.ValidationError(`Conditions conflict on field  - ${booleanFeature}`);
            }
        }
    }
}

/**
 * getOneGame - get available game by game code
 */
export async function getOneGame(entity: BaseEntity,
                                 gameCode: string,
                                 options?: GetOneGameOptions): Promise<EntityGameInfo> {
    const entityGame: EntityGame = await findOneEntityGame(entity, gameCode);
    const info = entityGame.toInfo();
    if (options?.addAggregatedFinalLimits && entity.isBrand()) {
        const entityCurrencies = entity.getCurrencies();
        const settings = await getEntitySettings(entity.path);
        if (options.currency && entityCurrencies.includes(options.currency)) {
            info.limits = {
                [options.currency]: await getAggregatedLimits(
                    entity as BrandEntity,
                    entityGame,
                    settings,
                    options.currency,
                    options.gameGroupName,
                    options.segmentId,
                    options.skipJurisdictionFiltering
                )
            };
        } else if (options.currencies) {
            info.limits = {};
            for (const currency of options.currencies) {
                if (entityCurrencies.includes(currency)) {
                    info.limits[currency] = await getAggregatedLimits(
                        entity as BrandEntity,
                        entityGame,
                        settings,
                        currency,
                        options.gameGroupName,
                        options.segmentId,
                        options.skipJurisdictionFiltering
                    );
                }
            }
        } else {
            const gameCurrencies = new Set(Object.keys(info.limits));
            const currencies = entityCurrencies.filter(currency => gameCurrencies.has(currency));
            for (const currency of currencies) {
                info.limits[currency] = await getAggregatedLimits(
                    entity as BrandEntity,
                    entityGame,
                    settings,
                    currency,
                    options.gameGroupName,
                    options.segmentId,
                    options.skipJurisdictionFiltering
                );
            }
        }
    }

    return info;
}

async function getAggregatedLimits(
    brand: BrandEntity,
    entityGame: EntityGame,
    settings: EntitySettings,
    currency: string,
    gameGroupName?: string,
    segmentId?: number,
    skipJurisdictionFiltering?: boolean
): Promise<Limits> {
    let limits: Limits;
    if (settings.newLimitsEnabled && !entityGame.settings?.newLimitsDisabled) {
        limits = await getNewLimitsFacade(brand).buildGameLaunch(
            entityGame.game,
            currency,
            await getGameGroupId(brand, gameGroupName, settings),
            segmentId,
            settings.isMarketplaceSupported,
            true,
            skipJurisdictionFiltering
        );
    } else {
        [limits] = await findPlayerLimits(
            brand,
            entityGame,
            currency,
            gameGroupName,
            settings,
            skipJurisdictionFiltering
        );
    }
    return limits;
}

/**
 * AddGameToEntity - function to add game to entity
 *
 */
export async function addGameToEntity(keyEntity: BaseEntity,
                                      options: FindEntityOptions,
                                      gameCode: string,
                                      isLiveGame: boolean,
                                      gameData?: EntityGameData): Promise<EntityGameCodeInfo> {
    const entity: Entity = keyEntity.find(options) as Entity;
    if (!entity) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }
    const parent = entity.getParent();
    const parentGame = await findOneEntityGame(parent, gameCode);

    if ((isLiveGame && parentGame.game.type !== GAME_TYPES.live) ||
        (!isLiveGame && parentGame.game.type === GAME_TYPES.live)) {
        return Promise.reject(new Errors.ParentGameTypeNotMatchError());
    }

    const entityGame = cloneDeep(parentGame);

    // allow to set entityGame status using the param from payload only if parent's game is in status NORMAL,
    // otherwise it should default to parent entity game status
    entityGame.status = parentGame.status === ENTITY_GAME_STATUS.NORMAL ? gameData?.status : parentGame.status;
    entityGame.settings = gameData?.settings;
    entityGame.title = gameData?.title;
    entityGame.features = gameData?.features;

    const limitFilters = gameData?.limitFilters;
    if (limitFilters) {
        validateLimitFilters(parentGame.game.type, limitFilters, parentGame.game.limits);
        entityGame.limitFilters = limitFilters;
    }

    const urlParams = gameData?.urlParams;
    if (urlParams) {
        if (typeof urlParams !== "object" || Array.isArray(urlParams)) {
            throw new Errors.ValidationError("urlParams must be object");
        }
        entityGame.urlParams = urlParams;
    }

    // validate game settings
    const mergedSettings = mergeArrayToObject([parentGame.settings, entityGame.settings]);
    await validateGameSettings(parentGame.game, mergedSettings,
        entity.isBrand() && entityGame.status === "normal", entity);

    const externalGameId = gameData?.externalGameId;
    if (externalGameId) {
        entityGame.externalGameId = externalGameId;
    }

    const domain = gameData?.domain;
    if (domain) {
        entityGame.domain = domain;
    }

    validateGameCurrenciesWithEntityCurrencies(entity, [entityGame]);
    return addEntityGame(entity, entityGame);
}

/**
 * AddGamesToEntity - function to add games to entity
 *
 */
export async function addGamesToEntity(entity: ChildEntity,
                                       data: GameCodesData,
                                       isLiveGame: boolean): Promise<EntityGameCodeInfo[]> {

    const queries = new Map();
    queries.set("game", { code: { [Op.in]: data.codes } });

    const parent = entity.getParent();

    let parentEntityGames: EntityGame[] = await findAllEntityGames(parent, queries);
    if (!parentEntityGames.length) {
        return Promise.reject(new Errors.GamesNotFound());
    }
    parentEntityGames = parentEntityGames.filter(entityGame => isLiveGame ?
        entityGame.game.type === GAME_TYPES.live :
        entityGame.game.type !== GAME_TYPES.live
    );
    if ((isLiveGame && !parentEntityGames.length) ||
        (!isLiveGame && !parentEntityGames.length)) {
        return Promise.reject(new Errors.ParentGameTypeNotMatchError());
    }

    const entityGames: EntityGame[] = await findAllEntityGames(entity, queries);
    const entityGameIdMap = new Map();
    entityGames.forEach(entityGame => entityGameIdMap.set(entityGame.gameId, entityGame));

    const newParentEntityGames: EntityGame[] = parentEntityGames.filter(
        parentEntityGame => !entityGameIdMap.has(parentEntityGame.gameId));

    if (entity.isBrand()) {
        const missingSettings = checkSettingsReady(newParentEntityGames);
        if (missingSettings) {
            return Promise.reject(new Errors.ValidationError("Missing parent settings: " + missingSettings));
        }
    }

    validateGameCurrenciesWithEntityCurrencies(entity, newParentEntityGames);

    await db.transaction(async (transaction: Transaction): Promise<any> => {
        for (const parentEntityGame of newParentEntityGames) {
            const newEntGame = cloneDeep(parentEntityGame);
            newEntGame.settings = undefined;
            newEntGame.urlParams = undefined;
            await addEntityGame(entity, newEntGame, transaction);
        }
    });
    return newParentEntityGames.map(entityGame => entityGame.toCodeInfo());
}

export async function addGamesToAllEntities(entity: ChildEntity,
                                            data: GameCodesData): Promise<BulkAddEntityGamesResult> {

    const gamesArray: string = `{${data.codes.join(",")}}`;
    try {
        const countQueryResult: any = await db.query(
            ADD_GAMES_SQL,
            {
                replacements: {
                    entityId: entity.id,
                    gameCodes: gamesArray
                },
                plain: true,
                useMaster: true
            }
        );
        const addedGamesCount: number = +countQueryResult.count;
        return { addedGamesCount };
    } catch (err) {
        if (err.message && err.message.startsWith("Jackpot settings is not valid for game")) {
            throw new Errors.ValidationError("Missing parent settings", [err]);
        }
        throw err;
    } finally {
        await LobbyCache.reset();
        await favoriteGamesCache.reset();
    }
}

function checkSettingsReady(entityGames: EntityGame[]): string[] {
    const notReady = [];
    for (const entGame of entityGames) {
        if (!entGame.isSuspended() && !entGame.settingsReady()) {
            notReady.push(entGame.game.code);
        }
    }
    return notReady.length ? notReady : undefined;
}

export function toBriefInfo(gamesList: any[]) {
    for (const game of gamesList) {
        delete game["settings"];
        delete game["clientFeatures"];
        delete game["historyRenderType"];
        delete game["historyUrl"];
        delete game["schemaDefinitionId"];
    }
    return gamesList;
}

/**
 * findOneDbItem - Get single game
 * @param query
 * @param options
 * @returns {Promise<Game>}
 */
export async function findOne(query: any, options: FindOneGameOptions = {}): Promise<Game> {
    const queryOptions = {
        where: query,
        include: [
            {
                model: GameProviderModel,
            }
        ],
    };

    const instance = await GameModel.findOne(queryOptions);

    if (!instance) {
        const gameCode = query && typeof query.code === "string" ? query.code : "not found gameCode";
        return Promise.reject(new Errors.GameNotFoundError(gameCode));
    }
    return new GameImpl(instance);
}

/**
 * findAllEntityGames - Get all available optionally filtered entity games
 * @param entity
 * @param queries
 * @param isShortInfo
 * @returns {Promise<EntityGame[]>}
 */
export async function findAllEntityGames(entity: BaseEntity,
                                         queries: Map<string, WhereOptions<any>> = new Map(),
                                         isShortInfo: boolean = false): Promise<GamesList> {

    const whereOptions = cloneDeep(queries);
    whereOptions.set("entity", { entityId: entity.id, ...whereOptions.get("entity") });
    whereOptions.set("game", { status: "available", ...whereOptions.get("game") });
    whereOptions.set("gameProvider", { status: "normal", ...whereOptions.get("gameProvider") });

    const category = await FindGamesHelper.decorateWhereOptionsOfGamesWithCategories(entity, queries, whereOptions);

    const includeGameModelQuery: IncludeOptions =
        FindGamesHelper.gamesToIncludeOptions(queries, whereOptions, isShortInfo);

    const filter = queries.get("filter") || {};
    let sortBy = getSortBy(filter["sortBy"] as string || "code");
    const sortOrder = filter["sortOrder"] || SORT_ORDER.DESC;

    if (sortBy === RELEASE_DATE_FIELD) {
        sortBy = RELEASE_DATE_SORT_KEY;
    }

    const findOptions: FindAndCountOptions<any> = {
        where: whereOptions.get("entity"),
        include: [includeGameModelQuery],

        offset: FilterService.valueFromQuery(filter, "offset"),
        limit: FilterService.valueFromQuery(filter, "limit"),
        order: literal(`"${sortBy}" ${sortOrder}`),
        distinct: true
    };

    let games: GamesList = await PagingHelper.findAndCountAll(EntityGameModel, findOptions,
        (item: EntityGameDBInstance) => new EntityGameImpl(item));

    games = FindGamesHelper.filterGamesByCategoryIfNeeded(filter, category, games);

    await FindGamesHelper.mergeGameAndEntityGamesSettings(entity, games);

    FindGamesHelper.adjustFilteredLimitsIfNeeded(queries, games);

    return games;
}

export async function findAllEntityGameInfos(entity: BaseEntity,
                                             queries: Map<string, WhereOptions<any>> = new Map(),
                                             isShortInfo: boolean = false): Promise<EntityGameInfo[]> {

    const games = await findAllEntityGames(entity, queries, isShortInfo);
    return games.map((entityGame: EntityGame) => entityGame.toInfo());
}

export async function findAllGameRelatedToRush(entity: BaseEntity,
                                               gameCodes: string[]) {

    const games = await GameModel.findAll({
        where: {
            code: { [Op.in]: gameCodes }
        },
        attributes: ["code", "title", "features"],
        include: [
            {
                model: GameProviderModel,
                attributes: ["id"],
                required: true,
            },
            {
                model: EntityGameModel,
                attributes: ["id"],
                where: { entityId: entity.id },
                required: false,
            }
        ]
    });

    return games.map(item => {
        return {
            code: item.get("code"),
            title: item.get("title"),
            features: item.get("features"),
            isAddedToEntity: Boolean(item.get("entityGames").length)
        };
    });
}

class FindGamesHelper {
    public static addLabelsToIncludeOptions(whereOptionsMap: Map<string, WhereOptions<any>>): IncludeOptions {
        let labelModelIncludeOptions: IncludeOptions = {
            model: LabelModel,
            required: false
        };

        const queryLabel: WhereOptions<any> = whereOptionsMap.get("label");
        if (!isEmpty(queryLabel)) {
            labelModelIncludeOptions = {
                ...labelModelIncludeOptions,
                where: queryLabel,
                required: true
            };
        }
        return labelModelIncludeOptions;
    }

    // TODO: try to remove this logic
    public static async decorateWhereOptionsOfGamesWithCategories(entity: BaseEntity,
                                                                  queries: Map<string, WhereOptions<any>>,
                                                                  whereOptionsMap: Map<string, WhereOptions<any>>)
        : Promise<GameCategory> {

        let category: GameCategory;
        const categoryQuery: any = queries.get("gameCategory");
        if (categoryQuery && categoryQuery.ids) {
            let entityGamesByCategory: EntityGame[] = [];
            for (const id of categoryQuery.ids) {
                category = await new GameCategoryService(entity).findOneById(id);
                const games = await GameCategoryGamesService.findAllForCategory(entity.id, category);
                entityGamesByCategory = entityGamesByCategory.concat(games);
            }
            whereOptionsMap.set("game", { ...queries.get("game"), id: entityGamesByCategory.map(c => c.gameId) });
        }
        return category;
    }

    // TODO: try to remove this logic
    public static filterGamesByCategoryIfNeeded(filter: WhereOptions<any>,
                                                category: GameCategory,
                                                games: GamesList): GamesList {
        if (filter["sortBy"] === "categoryList" && category) {
            const filteredGames = GameCategoryItemsService.filterGamesByItems(games, category.items);
            const sortOrder = filter["sortOrder"] || SORT_ORDER.DESC;
            if (sortOrder === SORT_ORDER.DESC) {
                filteredGames.reverse();
            }
            games = PagingHelper.copyInfo(filteredGames, games);
        }
        return games;
    }

    public static async mergeGameAndEntityGamesSettings(entity: BaseEntity, games: GamesList): Promise<void> {
        // fetch game settings
        const gamesSettings = await getGameSettings(entity);

        for (const game of games) {
            const entityGame = game as EntityGame;
            if (gamesSettings[entityGame.gameId]) {
                entityGame.settings = mergeArrayToObject(gamesSettings[entityGame.gameId]);
            }
        }
    }

    public static gamesToIncludeOptions(queries: Map<string, WhereOptions<any>>,
                                        whereOptionsMap: Map<string, WhereOptions<any>>,
                                        isShortInfo: boolean = false,
                                        attributesToExclude: GameModelAttributesToExclude
                                            = DEFAULT_GAME_MODEL_ATTRIBUTES_TO_EXCLUDE): IncludeOptions {

        const relatedGameModelQueries: IncludeOptions[] = [
            {
                model: GameProviderModel,
                where: whereOptionsMap.get("gameProvider"),
                attributes: { exclude: [...attributesToExclude.related] }
            }
        ];

        relatedGameModelQueries.push(FindGamesHelper.addLabelsToIncludeOptions(whereOptionsMap));

        const includeGameModelQuery: IncludeOptions = {
            model: GameModel,
            where: whereOptionsMap.get("game"),
            include: relatedGameModelQueries,
            attributes: { exclude: [...attributesToExclude.main] }
        };

        if (isShortInfo) {
            includeGameModelQuery.attributes["exclude"].push("limits");
        } else if (queries.get("currency")) {
            // case when we need limits only for certain currency
            // so we strip off all limits
            includeGameModelQuery.attributes["exclude"].push("limits");
            // ... and select limits of certain currency
            includeGameModelQuery.attributes["include"] = [
                [literal(`game.limits -> '${queries.get("currency")}'`), "limits"]
            ];
        }

        return includeGameModelQuery;
    }

    /**
     * In case of we filtered limits during select from PG we need adjust structure of returned limits if they exist.
     * This is needed because during limits selection in JSONB field it selects data under certain currency with '->'
     * operator
     */
    public static adjustFilteredLimitsIfNeeded(queries: Map<string, any>, games: GamesList): void {
        if (!queries.get("currency")) {
            return;
        }

        for (const game of games) {
            const entityGame = game as EntityGame;
            if (entityGame.game && entityGame.game.limits) {
                entityGame.game.limits = { [queries.get("currency")]: entityGame.game.limits };
            }
        }
    }
}

function getSortBy(sortBy: string) {
    switch (sortBy) {
        case "providerTitle":
            return "game.gameProvider.title";
        case "providerCode":
            return "game.gameProvider.code";
        case "categoryList":
            return "game.code";
        default:
            return "game." + sortBy;
    }
}

async function getGameSettings(entity: BaseEntity) {
    const parentEntityIds: number[] = getParentIds(entity);
    parentEntityIds.unshift(entity.id);

    const parentEntities: EntityGameDBInstance[] = await EntityGameModel.findAll({
        where: {
            entityId: { [Op.in]: parentEntityIds },
            settings: { [Op.not]: null }
        }
    });
    parentEntities.sort((a, b) => {
        const i1 = parentEntityIds.indexOf(a.get("entityId"));
        const i2 = parentEntityIds.indexOf(b.get("entityId"));
        return i2 - i1;
    });
    const gamesSettings = {};
    for (const parentEnt of parentEntities) {
        const gameId = parentEnt.get("gameId");
        if (!gamesSettings[gameId]) {
            gamesSettings[gameId] = [];
        }
        gamesSettings[gameId].push(parentEnt.get("settings"));
    }
    return gamesSettings;
}

/**
 * findOneEntityGame - get Game
 * @param entity
 * @param gameCode
 * @param transaction
 * @param includeParentGame
 * @param ignoreEntityGameNotFound
 * @returns {Promise<any>}
 */
export async function findOneEntityGame(entity: BaseEntity,
                                        gameCode: string,
                                        transaction?: Transaction,
                                        includeParentGame = false,
                                        ignoreEntityGameNotFound?: boolean): Promise<EntityGame | undefined> {

    const entities: number[] = getParentIds(entity);
    entities.unshift(entity.id);

    let game = await fetchGameBySWGameCode(gameCode, entities, transaction);

    // If no game was found, try to fetch by gameEntity externalGameId
    if (!game) {
        game = await fetchGameByExternalGameId(gameCode, entities, transaction);
    }

    if (!game) {
        return Promise.reject(new Errors.GameNotFoundError(gameCode));
    }

    const entityGames = game.get("entityGames").sort((a, b) => {
        // Sort games by descending
        const i1 = entities.indexOf(a.get("entityId"));
        const i2 = entities.indexOf(b.get("entityId"));
        return i2 - i1;
    });

    if (!entityGames || !entityGames.length || entityGames[entityGames.length - 1].get("entityId") !== entity.id) {
        if (ignoreEntityGameNotFound) {
            return undefined;
        }
        return Promise.reject(new Errors.EntityGameNotFound(gameCode));
    }

    const entityGame = new EntityGameImpl(entityGames[entityGames.length - 1]);

    if (includeParentGame && entityGames.length > 1) {
        entityGame.setParentGame(new EntityGameImpl(entityGames[entityGames.length - 2]));
    }

    entityGame.game = new GameImpl(game);

    for (const key of ["settings", "limitFilters", "urlParams"]) {
        const gameKeyValue = mergeArrayToObject(entityGames.map(v => v[key]));
        if (gameKeyValue) {
            entityGame[key] = gameKeyValue;
        }
    }

    for (let i = entityGames.length - 2; !entityGame.domain && i >= 0; --i) {
        entityGame.domain = entityGames[i].domain;
    }

    return entityGame;
}

async function fetchGameBySWGameCode(gameCode: string, entities: number[], transaction?: Transaction) {
    const queryEntityGame: WhereOptions<any> = {
        entityId: { [Op.in]: entities }
    };
    const includeEntityGameOptions: IncludeOptions = {
        model: EntityGameModel,
        required: false,
        where: queryEntityGame
    };
    const queryGame: WhereOptions<any> = {
        code: gameCode,
        status: "available"
    };
    return await fetchGame(queryGame, includeEntityGameOptions, transaction);
}

async function fetchGameByExternalGameId(externalGameId: string, entities: number[], transaction?: Transaction) {
    const queryEntityGame: WhereOptions<any> = {
        entityId: { [Op.in]: entities },
        externalGameId
    };
    const includeEntityGameOptions: IncludeOptions = {
        model: EntityGameModel,
        required: true,
        where: queryEntityGame
    };
    const queryGame: WhereOptions<any> = {
        status: "available"
    };
    return await fetchGame(queryGame, includeEntityGameOptions, transaction);
}

async function fetchGame(queryGame: WhereOptions<any>,
                         includeEntityGameOptions: IncludeOptions,
                         transaction?: Transaction) {
    const queryGameProvider: WhereOptions<any> = {
        status: "normal"
    };
    return await GameModel.findOne({
        transaction: transaction,
        include: [
            includeEntityGameOptions,
            {
                model: GameProviderModel,
                where: queryGameProvider,
            },
            {
                model: LabelModel,
                required: false,
            },
        ],
        where: queryGame
    });
}

export async function addEntityGame(entity: BaseEntity, parentEntityGame: EntityGame,
                                    transaction?: Transaction): Promise<EntityGameCodeInfo> {
    const newEntityGame = cloneDeep(parentEntityGame);
    if (entity.isBrand()) {
        newEntityGame.status = newEntityGame.status ? newEntityGame.status : "normal";
    }

    const createOptions: CreateOptions = {};
    if (transaction) {
        createOptions.transaction = transaction;
    }

    const addedGame = await EntityGameModel.findOne({
        where: { entityId: entity.id, gameId: newEntityGame.gameId },
    });

    if (addedGame) {
        return Promise.reject(new Errors.GameAlreadyExistsError());
    }

    try {
        await EntityGameModel.create({
            entityId: entity.id,
            gameId: newEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            status: newEntityGame.status,
            settings: newEntityGame.settings,
            limitFilters: newEntityGame.limitFilters,
            urlParams: newEntityGame.urlParams,
            externalGameId: newEntityGame.externalGameId,
            domain: newEntityGame.domain,
            title: newEntityGame.title,
            features: newEntityGame.features
        }, createOptions);

        const created = await findOneEntityGame(entity, newEntityGame.game.code, transaction);
        gameRTPHistoryService.get().logEntityGame(created);

        await addNewGameToExistingGameGroups(entity, created, transaction);
        return created.toCodeInfo();
    } catch (err) {
        if (err instanceof UniqueConstraintError) {
            return Promise.reject(new Errors.GameAlreadyExistsError());
        }
        return Promise.reject(err);
    } finally {
        await LobbyCache.reset();
        await favoriteGamesCache.reset();
    }
}

async function validateGameFeaturesLive(features: GameFeatures): Promise<string[]> {
    if (!features.live) {
        return [];
    }

    const errors = [];

    if (isLiveRush(features.live)) {
        const liveRush = features.live as LiveRush;
        if (!liveRush.type || typeof liveRush.type !== "string") {
            errors.push("live.type is not valid");
        }
        const { enableTip, displayWinAmount, leaveChipsOfWinsOnTable } = liveRush;
        if (typeof enableTip !== "undefined" && typeof enableTip !== "boolean") {
            errors.push("enableTip should be boolean");
        }
        if (typeof displayWinAmount !== "undefined" && typeof displayWinAmount !== "boolean") {
            errors.push("displayWinAmount should be boolean");
        }
        if (typeof leaveChipsOfWinsOnTable !== "undefined" && typeof leaveChipsOfWinsOnTable !== "boolean") {
            errors.push("leaveChipsOfWinsOnTable should be boolean");
        }
        for await (const table of liveRush.tables) {
            if (!table.tableId || typeof table.tableId !== "string") {
                errors.push("live.table.tableId is not valid");
            }
            if (!table.provider || typeof table.provider !== "string") {
                errors.push(`live.tables[${table.tableId}].provider is not valid`);
            }
            if (table.providerSettings && typeof table.providerSettings !== "object") {
                errors.push(`live.tables[${table.tableId}].providerSettings is not valid`);
            }
            if (table.selectionIntervalAfterNoMoreBets && !Number.isFinite(table.selectionIntervalAfterNoMoreBets)) {
                errors.push("selectionIntervalAfterNoMoreBets should be a number");
            }
            if (table.selectionIntervalBeforeNoMoreBets && !Number.isFinite(table.selectionIntervalBeforeNoMoreBets)) {
                errors.push("selectionIntervalBeforeNoMoreBets should be a number");
            }
            if (!Number.isFinite(table.rushOrder)) {
                errors.push(`live.tables[${table.tableId}].rushOrder should be a number`);
            }
            if (!table.gameCode) {
                errors.push(`live.tables[${table.tableId}].gameCode is empty`);
            } else {
                const game = await GameModel.findOne({
                    where: {
                        code: table.gameCode
                    },
                    attributes: ["features"]
                });
                const gameFeatures = game ? game.get("features") : false;
                if (!gameFeatures ||
                    !gameFeatures.live ||
                    gameFeatures.live.tableId !== table.tableId ||
                    gameFeatures.live.provider !== table.provider) {
                    errors.push(`live.tables[${table.tableId}].gameCode is not valid`);
                }
            }
        }
    } else {
        const live = features.live as LiveTable;
        if (!live.tableId || typeof live.tableId !== "string") {
            errors.push("live.tableId is not valid");
        }
        if (!live.provider || typeof live.provider !== "string") {
            errors.push("live.provider is not valid");
        }
        if (live.providerSettings && typeof live.providerSettings !== "object") {
            errors.push("live.providerSettings is not valid");
        }
    }

    return errors;
}

function validateRTP(rtp: number, errors: string[]) {
    if (!Number.isFinite(rtp)) {
        errors.push("rtp is not a number");
    } else if (rtp < 0 || rtp > 100) {
        errors.push("rtp should be in range [0, 100]");
    }
}

export async function validateGameFeatures(
    gameType: string,
    features: GameFeatures,
    isExternalJackpotSupported?: boolean
): Promise<GameFeatures> {
    if (!features) {
        return {};
    }

    let liveErrors = [];
    if (gameType === GAME_TYPES.live) {
        liveErrors = await validateGameFeaturesLive(features);
    }
    const translationsErrors = validateGameFeaturesTranslations(features);

    const errors = [...liveErrors, ...translationsErrors];

    if (features.jackpotTypes) {
        if (!Array.isArray(features.jackpotTypes)) {
            errors.push("jackpotTypes attribute is not array");
        } else if (!features.jackpotTypes.length) {
            errors.push("jackpotTypes attribute cannot be empty");
        }
    }

    if ("highestPrizeProbability" in features && typeof features.highestPrizeProbability !== "number") {
        errors.push("highestPrizeProbability should be a number");
    }

    if ("highestWin" in features && !validatePositiveNumber(features.highestWin)) {
        errors.push("highestWin should be a positive number");
    }

    if (features.baseRTP !== undefined) {
        validateRTP(features.baseRTP, errors);
    }

    if (features.baseRTPRange !== undefined) {
        validateRTP(features.baseRTPRange.min, errors);
        validateRTP(features.baseRTPRange.max, errors);
    }

    if (features.jpRTP !== undefined) {
        validateRTP(features.jpRTP, errors);
    }

    if (features.gameFinalizationType !== undefined) {
        const types: string[] = Object.keys(GameFinalizationType).map(k => GameFinalizationType[k]);
        if (!types.includes(features.gameFinalizationType)) {
            errors.push(`gameFinalizationType should one from ${types}`);
        }
    }

    if (features.featuresRTP) {
        validateFeaturesRTP(features.featuresRTP, errors);
    }

    if (features.validateRequestsExtensionEnabled !== undefined
        && typeof features.validateRequestsExtensionEnabled !== "boolean") {
        errors.push("validateRequestsExtensionEnabled should be boolean");
    }

    if (features.zeroBetCheckEnabled !== undefined
        && typeof features.zeroBetCheckEnabled !== "boolean") {
        errors.push("zeroBetCheckEnabled should be boolean");
    }

    if (errors.length) {
        return Promise.reject(new Errors.ValidationError(errors));
    }

    await JackpotService.validateJackpotTypes(features.jackpotTypes, isExternalJackpotSupported);

    return features;
}

export function validateGameFeaturesTranslations(features: GameFeatures): string[] {
    if (!("translations" in features)) {
        return [];
    }
    return validateTranslations(features.translations, "features");
}

export async function validateGameSettings(game: Game, settings: GameSettings,
                                           checkStrict?: boolean, entity?: BaseEntity): Promise<GameSettings> {
    await validateJackpots(game.features.jackpotTypes, settings?.jackpotId, entity,
        checkStrict && !game.features.ignoreJackpotTypesValidation,
        !game.features.isExternalJackpotSupported);

    const rtpDeduction = settings?.rtpConfigurator?.rtpDeduction;
    if (rtpDeduction !== undefined && rtpDeduction !== null) {
        if (!Number.isFinite(rtpDeduction)) {
            return Promise.reject(new Errors.ValidationError("rtpDeduction is not a number"));
        } else if (rtpDeduction < 0 || rtpDeduction > 100) {
            return Promise.reject(new Errors.ValidationError("rtpDeduction must be in range [0, 100]"));
        }
    }

    const sgtExpiresIn = settings?.startGameTokenExpiresIn;
    if (settings && sgtExpiresIn !== undefined && (sgtExpiresIn <= 0 || !Number.isSafeInteger(sgtExpiresIn))) {
        throw new Errors.ValidationError("startGameTokenExpiresIn should be a positive value");
    }

    const entitySettings = entity ? await getEntitySettings(entity.path) : {} as EntitySettings;
    await validateMarketingContributions(settings?.marketing, entitySettings);

    return settings;
}

export async function validateMarketingContributions(marketing: Marketing, entitySettings: EntitySettings) {
    const marketingContributions = marketing ? marketing.contributions : undefined;
    if (marketingContributions) {
        if (!Array.isArray(marketingContributions)) {
            return Promise.reject(new Errors.ValidationError("marketing.contributions is not an array"));
        } else {
            for (const item of marketingContributions) {
                if (!Number.isFinite(item.contribution) || item.contribution <= 0) {
                    return Promise.reject(
                        new Errors.ValidationError("marketing contribution is not a positive number"));
                }
                if (!item.jackpotId) {
                    return Promise.reject(new Errors.ValidationError("marketing jackpotId is missing"));
                }
            }
            await JackpotService.validateJackpotsExist(
                marketingContributions.map((item) => item.jackpotId),
                entitySettings
            );
        }
    }
}

async function validateJackpots(jackpotTypes: string[],
                                jackpots: JackpotMapping,
                                entity?: BaseEntity,
                                checkMatchingJackpotTypes?: boolean,
                                checkMatchingJackpotIds?: boolean) {
    if ((!jackpotTypes && !jackpots) || (jackpotTypes && !jackpots && !checkMatchingJackpotTypes)) {
        return;
    }

    if (!jackpotTypes && jackpots && checkMatchingJackpotIds) {
        throw new Errors.ValidationError("jackpotId setting is not allowed for non-jackpot game");
    }

    if (jackpotTypes && (!jackpots || isEmpty(jackpots)) && checkMatchingJackpotTypes) {
        throw new Errors.ValidationError("jackpotId setting is missing");
    }

    if ((typeof jackpots) !== "object" || Array.isArray(jackpots)) {
        throw new Errors.ValidationError("jackpotId setting should be a object");
    }

    for (const type of jackpotTypes) {
        const jackpotId = jackpots[type];
        if (jackpotId && (typeof jackpotId) !== "string") {
            throw new Errors.ValidationError(`jackpotId ${jackpotId} setting is not valid`);
        }
        if (!jackpotId && checkMatchingJackpotTypes) {
            throw new Errors.ValidationError(`jackpotId setting for this ${type} type is missing`);
        }
    }

    await JackpotService.validateJackpotIds(jackpots, entity, checkMatchingJackpotIds);

}

export async function getAvailableGameCodes(entity: BaseEntity, features?: GameFeatures): Promise<string[]> {
    const queryEntityGame: WhereOptions<any> = {
        entityId: { [Op.eq]: entity.id },
        status: { [Op.eq]: "normal" },
    };
    const queryGames: WhereOptions<any> = {
        status: { [Op.eq]: "available" },
    };
    if (features) {
        Object.assign(queryGames, parseGameFeaturesFilter(features));
    }
    const queryGamesProvider: WhereOptions<any> = {
        status: { [Op.eq]: "normal" },
    };
    const games = await GameModel.findAll({
        include: [
            {
                model: EntityGameModel,
                where: queryEntityGame,
            },
            {
                model: GameProviderModel,
                where: queryGamesProvider,
            }
        ],
        where: queryGames,
    });

    return games.map(game => game.get("code"));
}

export async function getAvailableGameCodesByProviderGameCode(providerGameCode: string,
                                                              providerCode: string,
                                                              limit?: number): Promise<string[]> {
    const queryGames: WhereOptions<any> = {
        status: "available",
        providerGameCode,
    };
    const games = await GameModel.findAll({
        include: [
            {
                model: GameProviderModel,
                where: {
                    status: "normal",
                    code: providerCode,
                },
            }
        ],
        where: queryGames,
        attributes: ["code"],
        ...(limit && { limit })
    });
    return games.map(game => game.get("code"));
}

export async function getAvailableGamesByCodes(codes: string[], entity: BaseEntity): Promise<GameImpl[]> {
    const queryEntityGame: WhereOptions<any> = {
        entityId: entity.id,
    };
    const queryGames: WhereOptions<any> = {
        code: { [Op.in]: codes },
        status: "available",
    };
    const queryGamesProvider: WhereOptions<any> = {
        status: "normal",
    };
    const games = await GameModel.findAll({
        include: [
            {
                model: EntityGameModel,
                where: queryEntityGame,
            },
            {
                model: GameProviderModel,
                where: queryGamesProvider,
            }
        ],
        where: queryGames,
    });

    return games.map(game => new GameImpl(game));
}

export async function validateGameCodes(codes: string[], brand: BaseEntity) {
    const games = await getAvailableGamesByCodes(codes, brand);

    const invalidCodes = [];

    for (const code of codes) {
        if (!games.some(game => game.code === code)) {
            invalidCodes.push(code);
        }
    }

    if (invalidCodes.length) {
        return Promise.reject(new Errors.ValidationError(`Invalid game codes - ${invalidCodes.join(", ")}`));
    }
}

function parseFeaturesJson(requestQuery: any): object {
    const featuresFilter = requestQuery["features"];
    let result = {};
    if (!featuresFilter) {
        return result;
    }

    try {
        result = JSON.parse(featuresFilter);
    } catch (e) {
        throw new Errors.ValidationError(`Features filter invalid JSON string - ${featuresFilter}`);
    }
    return result;
}

async function addNewGameToExistingGameGroups(entity: BaseEntity,
                                              game: EntityGame,
                                              transaction: Transaction): Promise<void> {
    try {
        const limitGroup: string = game.game.limitsGroup;
        if (!limitGroup || entity.isMaster()) {
            return;
        }
        if (entity.type !== ENTITY_TYPE.BRAND && entity.type !== ENTITY_TYPE.MERCHANT) {
            return;
        }
        const settings: EntitySettings = await getEntitySettings(entity.path, true);
        if (!settings.autoAddNewGamesToGroups) {
            return;
        }
        await db.query(
            "INSERT INTO game_group_limits(game_group_id, entity_game_id, game_id, limits, created_at, updated_at) " +
            "SELECT DISTINCT ON (game_group_id) game_group_id, :entityGameId, :gameId, limits, NOW(), NOW() FROM game_group_limits " +
            "WHERE entity_game_id IN (SELECT id FROM entity_games WHERE entity_id = :entityId AND game_id IN " +
            "(SELECT id FROM games WHERE limits_group = :limitsGroup AND provider_id = :providerId))",
            {
                replacements: {
                    entityGameId: game.id,
                    gameId: game.game.id,
                    entityId: entity.id,
                    limitsGroup: limitGroup,
                    providerId: game.game.providerId
                },
                transaction: transaction
            });
    } finally {
        gameCategoryGamesCache.reset();
        favoriteGamesCache.reset();
    }

}

export function validateGameCurrenciesWithEntityCurrencies(entity: ChildEntity, entityGames: EntityGame[]) {
    const errorMessages = [];
    entityGames.forEach(entityGame => {
        const currenciesSupport: string[] | boolean = entityGame?.game?.features?.currenciesSupport || false;
        if (Array.isArray(currenciesSupport)) {
            if (currenciesSupport.length) {
                const currencyNotExists = entity.getCurrencies().filter(code => !currenciesSupport.includes(code));
                if (currencyNotExists.length) {
                    errorMessages.push(
                        `Game "${entityGame.game.code}" doesn't support` +
                        ` "${currencyNotExists}" currencies from the operator list.`
                    );
                }
            } else {
                errorMessages.push(
                    `Game "${entityGame.game.code}" doesn't support` +
                    ` "${entity.getCurrencies()}" currencies from the operator list.`
                );
            }
        }
    });

    if (errorMessages.length) {
        throw new Errors.ValidationError(errorMessages);
    }
}

export async function validateGameRelations(entity: BaseEntity, entityGames: EntityGame[]): Promise<void> {
    const ids = entityGames.map(game => game.id);
    const limits = await getGameLimitsConfigurationService(entity).list(entity, {
        entityGameId: { [Op.in]: ids }
    });

    const gameGroupLimits = await GameGroupLimitModel.findAll({
        where: {
            entityGameId: { [Op.in]: ids }
        },
    });

    if (limits.length || gameGroupLimits.length) {
        return Promise.reject(new Errors.ValidationError("Some of entity game has game limits relation"));
    }
}

export async function removeGameRelations(entity: BaseEntity,
                                          entityGames: EntityGame[],
                                          transaction: Transaction): Promise<void> {
    await getGameLimitsConfigurationService(entity).destroyAll(entity, entityGames, transaction);

    await GameGroupLimitModel.destroy({
        where: {
            entityGameId: { [Op.in]: entityGames.map(i => i.id) }
        },
        transaction
    });
}

async function getGamesByPlayer(entity: BaseEntity,
                                playerCode: string,
                                query: FindOptions<any> = {},
                                isFavorite: boolean,
                                isRecently: boolean
) {
    const favorite = isFavorite ? { isFavorite: true } : {};
    const recently = isRecently ? { isRecently: true } : {};
    const where = {
        ...query,
        where: {
            entityId: entity.id,
            playerCode: playerCode,
            ...favorite,
            ...recently
        }
    };

    return await FavoriteGamesModel.findAll(where);
}

export async function getFavoriteGames(entity: BaseEntity, playerCode: string, query) {
    const findOptions: FindOptions<any> = {
        offset: valueFromQuery(query, "offset"),
        limit: valueFromQuery(query, "limit"),
    };
    const gameCodeOnly = query?.gameCodeOnly && query.gameCodeOnly === "true";
    const favoriteGames = await getGamesByPlayer(entity, playerCode, findOptions, true, false);
    const gameCodes = favoriteGames.map(item => item.toFavoriteGame());
    if (gameCodeOnly) {
        const availableGames: GameImpl[] = await getAvailableGamesByCodes(gameCodes, entity);
        return availableGames.map((game: GameImpl) => game.code);
    }

    const favoriteGamesForLobby = [];
    const key = FavoriteGamesCache.getKey(entity.id, playerCode);
    const games = await favoriteGamesCache.find(key, entity.id, findOptions, gameCodes);
    const settings = await getEntitySettings(entity.path);
    const entityCurrencies = entity.getCurrencies();
    for (const game of games) {
        const limits = await findGameLimitsByCurrencyCode(
            entity,
            game,
            undefined,
            undefined,
            entityCurrencies,
            settings
        );
        favoriteGamesForLobby.push({ ...game.toInfo(true), limits });
    }
    return favoriteGamesForLobby;
}

export async function getRecentlyPlayedGames(entity: BaseEntity, playerCode: string, query: FindOptions<any> = {}) {
    const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "DESC";
    const findOptions: FindOptions<any> = {
        offset: valueFromQuery(query, "offset"),
        limit: valueFromQuery(query, "limit"),
        order: literal(`"updated_at" ${sortOrder}`),
    };
    const games = await getGamesByPlayer(entity, playerCode, findOptions, false, true);
    return games.map(item => item.toRecentlyGame());
}

export async function getPlayerGameActivities(entity: BaseEntity, playerCode: string) {
    const games = await getGamesByPlayer(entity, playerCode, {}, false, false);
    return games.map(item => item.toInfo());
}

const UPSERT_RECENTLY_GAME_BY_PLAYER = "INSERT INTO favorite_games_by_player (" +
    "entity_id, player_code, game_code, is_favorite, is_recently, created_at, updated_at )" +
    "VALUES(:entityId, :playerCode, :gameCode, false, true, now(), now())" +
    "ON CONFLICT ( entity_id, player_code, game_code ) DO UPDATE " +
    "SET updated_at = now(), is_recently = true";

export async function upsertRecentlyGame(entityId: number, playerCode: string, gameCode: string) {
    const data = {
        entityId: entityId,
        playerCode: playerCode,
        gameCode: gameCode,
    };

    await db.query(UPSERT_RECENTLY_GAME_BY_PLAYER, { replacements: data });
    IoServerV4.notifyRecentlyGame(data);
}

const UPSERT_FAVORITE_GAME_BY_PLAYER = "INSERT INTO favorite_games_by_player (" +
    "entity_id, player_code, game_code, is_favorite, is_recently, created_at, updated_at )" +
    "VALUES(:entityId, :playerCode, :gameCode, :isFavorite, false, now(), now())" +
    "ON CONFLICT ( entity_id, player_code, game_code ) DO UPDATE " +
    "SET is_favorite = :isFavorite";

export async function upsertFavoriteGame(entity: BaseEntity,
                                         playerCode: string,
                                         gameCode: string,
                                         isFavorite: boolean) {
    await validateGameCodes([gameCode], entity);

    const data = {
        entityId: entity.id,
        playerCode: playerCode,
        gameCode: gameCode,
        isFavorite: isFavorite
    };
    await db.query(UPSERT_FAVORITE_GAME_BY_PLAYER, { replacements: data });
    favoriteGamesCache.reset(FavoriteGamesCache.getKey(entity.id, playerCode));
    IoServerV4.notifyFavoriteGame(data);
}

function validateFeaturesRTP(featuresRTP: FeaturesRTP, errors: string[]) {
    for (const featureName of Object.keys(featuresRTP)) {
        const feature = featuresRTP[featureName];
        if (feature.rtpReducer === undefined
            || feature.rtpReducer === null
            || typeof feature.rtpReducer !== "boolean") {
            errors.push(`${featureName} invalid value for rtpReducer`);
        }
        validateRTP(feature.RTP, errors);
    }
}

export async function upsertGamesRtpDeduction(entity: BaseEntity, listGames: BulkUpsertGamesRtp) {
    const listEntityGameInfo = []; // for history

    // If throw Error the transaction has already been rolled back automatically by Sequelize!
    await db.transaction(async (transaction: Transaction) => {
        for (const { gameCode, newRtpDeduction } of listGames) {
            const currentEntityGameInfo: EntityGame = await findOneEntityGame(entity, gameCode, transaction);
            const newEntityGameInfo = cloneDeep(currentEntityGameInfo);
            const { id } = newEntityGameInfo;

            if (newEntityGameInfo.settings) {
                const { settings } = newEntityGameInfo;
                if (settings.rtpConfigurator && typeof settings.rtpConfigurator === "object") {
                    settings.rtpConfigurator.rtpDeduction = newRtpDeduction;
                } else {
                    // not set rtpConfigurator
                    settings.rtpConfigurator = { rtpDeduction: newRtpDeduction };
                }
            } else {
                // not set settings
                newEntityGameInfo.settings = { rtpConfigurator: { rtpDeduction: newRtpDeduction } };
            }

            await EntityGameModel.update({ settings: newEntityGameInfo.settings }, { where: { id }, transaction });

            listEntityGameInfo.push({ currentEntityGameInfo, newEntityGameInfo });
        }
    });

    listEntityGameInfo.forEach(({ currentEntityGameInfo, newEntityGameInfo }) => {
        gameRTPHistoryService.get().logEntityGame(newEntityGameInfo, currentEntityGameInfo);
    });
}

/**
 * getAllLiveGames - Gets all available live games for entity
 *
 * @param entity
 * @param requestQuery
 * @param relativeUrl
 * @returns {Promise<EntityGameInfo[]>}
 */
export async function getAllLiveGames(entity: BaseEntity,
                                      requestQuery: WhereOptions<any> = {},
                                      relativeUrl?: string): Promise<EntityGameInfo[]> {

    requestQuery = { ...requestQuery, type: "live" };
    const queries = new Map();

    const filterConditions = parseFilter(requestQuery, ["limit", "offset", "sortBy", "sortOrder"]);
    filterConditions["sortBy"] = FilterService.getSortKey(filterConditions, sortableKeys, DEFAULT_SORT_KEY);
    filterConditions["sortOrder"] = FilterService.valueFromQuery(filterConditions, "sortOrder") || "ASC";
    queries.set("filter", filterConditions);

    queries.set("game", parseFilter(requestQuery, GameImpl.SEARCHABLE_KEYS));

    if (requestQuery["gameStatus"]) {
        queries.set("game", {
            ...queries.get("game"),
            status: requestQuery["gameStatus"]
        });
    }

    if (requestQuery["gamecategoryId__in"]) {
        queries.set("gameCategory", { ids: requestQuery["gamecategoryId__in"].toString().split(",") });
    } else if (requestQuery["gamecategoryId"]) {
        queries.set("gameCategory", { ids: [requestQuery["gamecategoryId"]] });
    } else if (requestQuery["onlyCategoryGames"] === "true") {
        const categories = await new GameCategoryService(entity).findAll();
        queries.set("gameCategory", { ids: categories.map(category => category.id) });
    }

    if (requestQuery["status"]) {
        queries.set("entity", { status: requestQuery["status"] });
    }

    if (requestQuery["providerId"]) {
        Object.assign(queries.get("game"), { "providerId": requestQuery["providerId"] });
    }

    Object.assign(queries.get("game"), parseGameFeaturesFilter(requestQuery));

    queries.set("label", LabelService.prepareSearchQuery(
        parseFilter(requestQuery, LabelService.getSearchableKeys())
    ));

    // case when we need limits only for certain currency
    if (requestQuery["currency"]) {
        queries.set("currency", requestQuery["currency"]);
    }

    let target: EntityGameInfo[];
    let source: GamesList;
    const includeSuspendedGamesFilter =
        requestQuery["includeSuspendedGames"] && requestQuery["includeSuspendedGames"] === true;
    const addGameStatus = includeSuspendedGamesFilter;
    source = await findAllLiveGames(
        entity,
        queries,
        requestQuery.shortInfo === "true",
        includeSuspendedGamesFilter
    );

    target = source.map((entityGame: EntityGame) => entityGame.toInfo(false, addGameStatus));
    if (requestQuery["jackpots"] === "true") {
        await JackpotService.appendJackpots(entity, target, requestQuery["jpCurrency"] as string);
    }
    if (requestQuery["includeLive"] === "true") {
        const urlManager = await buildDynamicLiveManagerUrl(entity);
        await LiveManagerService.addLiveInfo(urlManager, target, relativeUrl);
    }
    return PagingHelper.copyInfo(target, source);
}

/**
 * findAllEntityGames - Get all available optionally filtered entity games
 * @param entity
 * @param queries
 * @param isShortInfo
 * @param showAllGame - Only for common service!!!
 * @returns {Promise<EntityGame[]>}
 */
export async function findAllLiveGames(entity: BaseEntity,
                                       queries: Map<string, WhereOptions<any>> = new Map(),
                                       isShortInfo: boolean = false,
                                       showAllGame: boolean = false): Promise<GamesList> {

    let gameStatus = {};
    if (!showAllGame) {
        gameStatus = { status: "available" };
    }
    const whereOptions = cloneDeep(queries);
    whereOptions.set("entity", { entityId: SUPER_ADMIN_ID, ...whereOptions.get("entity") });
    whereOptions.set("game", { ...gameStatus, ...whereOptions.get("game") });
    whereOptions.set("gameProvider", { status: "normal", ...whereOptions.get("gameProvider") });

    const category = await FindGamesHelper.decorateWhereOptionsOfGamesWithCategories(entity, queries, whereOptions);

    const attrToExclude = {
        ...DEFAULT_GAME_MODEL_ATTRIBUTES_TO_EXCLUDE,
        main: []
    };
    const includeGameModelQuery: IncludeOptions =
        FindGamesHelper.gamesToIncludeOptions(queries, whereOptions, isShortInfo, attrToExclude);

    const filter = queries.get("filter") || {};
    let sortBy = getSortBy(filter["sortBy"] as string || "code");
    const sortOrder = filter["sortOrder"] || SORT_ORDER.DESC;

    if (sortBy === RELEASE_DATE_FIELD) {
        sortBy = RELEASE_DATE_SORT_KEY;
    }

    const findOptions: FindAndCountOptions<any> = {
        where: whereOptions.get("entity"),
        include: [includeGameModelQuery],

        offset: FilterService.valueFromQuery(filter, "offset"),
        limit: FilterService.valueFromQuery(filter, "limit"),
        order: literal(`"${sortBy}" ${sortOrder}`),
        distinct: true
    };

    let games: GamesList = await PagingHelper.findAndCountAll(EntityGameModel, findOptions,
        (item: EntityGameDBInstance) => new EntityGameImpl(item));

    games = FindGamesHelper.filterGamesByCategoryIfNeeded(filter, category, games);

    await FindGamesHelper.mergeGameAndEntityGamesSettings(entity, games);

    FindGamesHelper.adjustFilteredLimitsIfNeeded(queries, games);

    return games;
}

export async function isGameExists(gameCode: string): Promise<boolean> {
    const instance = await GameModel.findOne({ where: { code: gameCode }, attributes: ["id"] });
    return !!instance;
}

export async function findGameCodesByTableId(tableId: string) {
    const items = await GameModel.findAll({
        where:
            {
                features: {
                    [Op.contains]: {
                        live: {
                            tableId
                        }
                    } as any
                }
            },
        attributes: ["code", "physicalTableId"]
    });
    return items.map((item: any) => {
        return { code: item.code, physicalTableId: item.physicalTableId };
    });
}

export function isItgMiniGsGame(game: Game) {
    return game.code.startsWith(config.itgGameCodePrefix) && game.type !== GAME_TYPES.external;
}

export async function fetchGameCodesByTableIdAndEntityId(tableId: number, entityId: number): Promise<string[]> {
    const games = await GameModel.findAll({
        where: {
            physicalTableId: { tableId }
        },
        attributes: ["code"],
        include: [
            {
                model: EntityGameModel,
                attributes: ["id"],
                where: { entityId },
                required: false,
            }
        ]
    });

    return games.map(item => item.code);
}
