import { FreebetRewardService } from "./types/playerFreebetPromotion";
import { BonusCoinRewardService } from "./types/playerBonusCoinPromotion";
import * as Errors from "../../errors";
import { BrandEntity } from "../../entities/brand";
import { PromotionRewardService } from "./promotionRewardService";
import { BonusCoinsReward, Rewards } from "../../entities/player";
import EntityCache from "../../../skywind/cache/entity";
import { PlayerPromotionWalletFacade, PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";

export function get(brand: BrandEntity, promoType: string, sharedPromoEnabled = false): PromotionRewardService {
    if (promoType === PROMO_TYPE.FREEBET) {
        return new FreebetRewardService(brand, sharedPromoEnabled);
    } else if (promoType === PROMO_TYPE.BONUS_COIN) {
        return new BonusCoinRewardService(brand);
    } else {
        throw new Errors.ValidationError("This type of promo is not supported");
    }
}

export async function getRewards(brandId: string | number | BrandEntity,
                                 playerCode: string,
                                 currency: string): Promise<Rewards> {
    let brand: BrandEntity;

    if (Number.isInteger(+brandId)) {
        brand = await EntityCache.findById<BrandEntity>(+brandId);
    } else {
        brand = brandId as BrandEntity;
    }
    const rewards = {
        bonusCoins: [],
        freeBets: []
    };

    const playerWallet = PlayerPromotionWalletFacade.create(brand.id, playerCode, currency);
    const bonusCoinsPromoWallet = await playerWallet.getBonusCoinWallet();
    const freeBetsPromoWallet = await playerWallet.getFreebetWallet();

    const bonusCoinRewardService = new BonusCoinRewardService(brand);
    const freeBetsRewardService = new FreebetRewardService(brand);

    for (const promo of (await bonusCoinRewardService.getPromoInfo(bonusCoinsPromoWallet))) {
        promo.bonusCoins.map((item) => {
            const bns: BonusCoinsReward = {
                promoId: promo.promoId,
                amount: item.amount,
                rewardedAmount: item.rewardedAmount,
                redeemMinAmount: item.redeemMinAmount,
                games: item.games,
                startDate: item.startDate,
                endDate: item.endDate
            };
            if (item.redeemMaxAmount) {
                bns.redeemMaxAmount = item.redeemMaxAmount;
            }
            rewards.bonusCoins.push(bns);
        });
    }

    for (const promo of (await freeBetsRewardService.getPromoInfo(freeBetsPromoWallet))) {
        promo.freebets.map((item) => {
            rewards.freeBets.push({
                promoId: promo.promoId,
                amount: item.amount,
                games: item.games,
                currency: item.currency,
                startDate: item.startDate,
                endDate: item.endDate
            });
        });
    }

    return rewards;
}
