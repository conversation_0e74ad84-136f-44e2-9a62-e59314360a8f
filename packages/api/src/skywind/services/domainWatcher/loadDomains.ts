import { Op } from "sequelize";
import { Models } from "../../models/models";
import { EntityStatus } from "../../entities/entity";
import { DomainStatus, StaticDomainType } from "../../entities/domain";
import { AdapterDomains, DomainSource, DomainSources } from "./types";

interface DomainData {
    id: number;
    domain: string;
    type?: StaticDomainType;
}

interface PoolItemData {
    isActive?: boolean;
    blockedDate?: Date;
}

interface StaticDomainPoolData {
    id: number;
    domainWatcherAdapterId: string;
    domains?: Array<DomainData & {
        StaticDomainPoolItem?: PoolItemData;
    }>;
}

interface DynamicDomainPoolData {
    id: number;
    domainWatcherAdapterId?: string;
    domains?: Array<DomainData & {
        DynamicDomainPoolItem?: PoolItemData;
    }>;
}

interface EntityWithDomains {
    id: number;
    StaticDomainPoolModel?: StaticDomainPoolData;
    DynamicDomainPoolModel?: DynamicDomainPoolData;
    staticDomain?: DomainData;
    lobbyDomain?: DomainData;
    liveStreamingDomain?: DomainData;
    ehubDomain?: DomainData;
    DynamicDomainModel?: DomainData;
}

type AdapterDomainInfo = { domainId: number; domainWatcherAdapterId: string; sources: DomainSource[]; };

export async function loadDomains(): Promise<AdapterDomains> {
    const entities = await Models.EntityModel.findAll({
        where: {
            [Op.or]: [
                {
                    staticDomainPoolId: {
                        [Op.ne]: null
                    }
                },
                {
                    dynamicDomainPoolId: {
                        [Op.ne]: null
                    }
                }
            ],
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                where: {
                    domainWatcherAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "domainWatcherAdapterId"],
                required: false,
                include: [
                    {
                        model: Models.StaticDomainModel,
                        as: "domains",
                        where: {
                            status: DomainStatus.ACTIVE
                        },
                        through: {
                            attributes: ["isActive", "blockedDate"],
                            where: {
                                isActive: true,
                                blockedDate: null
                            }
                        },
                        attributes: ["id", "domain", "type"],
                        required: false
                    }
                ]
            },
            {
                model: Models.DynamicDomainPoolModel,
                where: {
                    domainWatcherAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "domainWatcherAdapterId"],
                required: false,
                include: [
                    {
                        model: Models.DynamicDomainModel,
                        as: "domains",
                        where: {
                            status: DomainStatus.ACTIVE
                        },
                        through: {
                            attributes: ["isActive", "blockedDate"],
                            where: {
                                isActive: true,
                                blockedDate: null
                            }
                        },
                        attributes: ["id", "domain"],
                        required: false
                    }
                ]
            },
            {
                model: Models.DynamicDomainModel,
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain"],
                required: false
            },
            {
                association: "staticDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain", "type"],
                required: false
            },
            {
                association: "lobbyDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain", "type"],
                required: false
            },
            {
                association: "liveStreamingDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain", "type"],
                required: false
            },
            {
                association: "ehubDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain", "type"],
                required: false
            }
        ],
        attributes: ["id", "dynamicDomainPoolId"]
    });

    const items = new Map<string, AdapterDomainInfo>();
    for (const entity of entities) {
        toDomains(entity.toJSON() as EntityWithDomains, items);
    }
    const adapterDomains: AdapterDomains = new Map();
    for (const [domain, { domainWatcherAdapterId, domainId, sources }] of items.entries()) {
        const adapterDomain: DomainSources = adapterDomains.get(domainWatcherAdapterId) || new Map();
        adapterDomain.set(domain, {
            domainId,
            sources: [
                ...(adapterDomain.get(domain)?.sources || []),
                ...sources
            ]
        });
        adapterDomains.set(domainWatcherAdapterId, adapterDomain);
    }
    return adapterDomains;
}

function toDomains(entity: EntityWithDomains, items: Map<string, AdapterDomainInfo>): void {
    const fnDomain = (domainWatcherAdapterId: string) => ({ id, domain, type }: DomainData, source?: Omit<DomainSource, "domainId" | "staticType">) => {
        const existing = items.get(domain);
        items.set(domain, {
            domainId: id,
            domainWatcherAdapterId,
            sources: [{ domainId: id, staticType: type, ...source }, ...existing?.sources ?? []]
        });
    };

    // Handle static domain pool
    if (entity.StaticDomainPoolModel?.domainWatcherAdapterId) {
        const { id, domainWatcherAdapterId, domains } = entity.StaticDomainPoolModel;
        const addDomain = fnDomain(domainWatcherAdapterId);

        if (Array.isArray(domains) && domains.length > 0) {
            for (const domain of domains) {
                addDomain(domain, { type: "staticPool" as const, poolId: id });
            }
        }
        if (entity.staticDomain) {
            addDomain(entity.staticDomain, { type: "entity" as const });
        }
        if (entity.lobbyDomain) {
            addDomain(entity.lobbyDomain, { type: "entity" as const });
        }
        if (entity.liveStreamingDomain) {
            addDomain(entity.liveStreamingDomain, { type: "entity" as const });
        }
        if (entity.ehubDomain) {
            addDomain(entity.ehubDomain, { type: "entity" as const });
        }
    }

    // Handle dynamic domain pool
    if (entity.DynamicDomainPoolModel?.domainWatcherAdapterId) {
        const { id: poolId, domainWatcherAdapterId, domains } = entity.DynamicDomainPoolModel;
        const addDomain = fnDomain(domainWatcherAdapterId);

        if (Array.isArray(domains) && domains.length > 0) {
            for (const domain of domains) {
                addDomain(domain, { type: "dynamicPool" as const, poolId });
            }
        }
        if (entity.DynamicDomainModel) {
            addDomain(entity.DynamicDomainModel, { type: "entity" as const });
        }
    }
}
