import { DomainInfo, RawDomainInfo } from "../entities/domain";

export function getDomainInfo(info: RawDomainInfo | null): DomainInfo | null {
    if (!info) {
        return null;
    }
    return {
        ...info,
        ...(info.monitoringStatus ? {
            monitoringStatus: Object.entries(info.monitoringStatus).reduce((result, [adapterId, status]) => ({
                ...result,
                [adapterId]: {
                    ...status,
                    lastCheckedAt: new Date(status.lastCheckedAt)
                }
            }), {})
        } : {})
    };
}

export function setDomainInfo({ monitoringStatus, ...info }: DomainInfo): RawDomainInfo {
    return {
        ...info,
        ...(monitoringStatus ? {
            monitoringStatus: Object.entries(monitoringStatus).reduce((result, [adapterId, status]) => ({
                ...result,
                [adapterId]: {
                    ...status,
                    lastCheckedAt: status.lastCheckedAt?.toISOString()
                }
            }), {})
        } : {})
    };
}
